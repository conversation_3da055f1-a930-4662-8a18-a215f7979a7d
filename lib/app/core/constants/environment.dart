import 'dart:io';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class Environment {
  // network
  static String get httpUrl =>
      dotenv.env['HTTP_URL'] ?? 'https://api.gomama.com.sg/api/v1';
  static String get httpZestUrl =>
      dotenv.env['ZEST_URL'] ??
      'https://api.zest.best/reviews/public/app-clients/90c3c4ec0842105aa5faa75c56097d6666fe7d22e5a0f57dc4';
  static String get scheme => dotenv.env['SCHEME'] ?? 'https';
  static String get host => dotenv.env['HOST'] ?? 'api.gomama.com.sg';
  static String get zestHost => dotenv.env['ZEST_HOST'] ?? 'api.zest.best';
  static int? get port =>
      dotenv.env['PORT'] != null ? int.tryParse(dotenv.env['PORT']!) : null;
  static int? get zestPort => dotenv.env['ZEST_PORT'] != null
      ? int.tryParse(dotenv.env['ZEST_PORT']!)
      : null;
  static String get ver => dotenv.env['VER'] ?? '/api/v1';
  static String get zestVer =>
      dotenv.env['ZEST_VER'] ??
      'reviews/public/app-clients/90c3c4ec0842105aa5faa75c56097d6666fe7d22e5a0f57dc4';
  static String get flavor => dotenv.env['FLAVOR'] ?? '';
  // singpass
  static String get singpassRedirectUrlIos =>
      dotenv.env['SINGPASS_REDIRECT_URI_IOS'] ?? '';
  static String get singpassRedirectUrlAndroid =>
      dotenv.env['SINGPASS_REDIRECT_URI_ANDROID'] ?? '';
  static String get singpassIntentUrl =>
      dotenv.env['SINGPASS_INTENT_URI'] ?? '';
  // customer service
  static String get gomamaWhatsappPhone =>
      dotenv.env['GOMAMA_WHATSAPP_PHONE'] ?? '';
  // google
  static String get googleMapKey => dotenv.env['GOOGLE_PLACES_KEY'] ?? '';
  static String get googleClientIdIos => dotenv.env['GOOGLE_CLIENT_ID_IOS'] ?? '';
  static String get googleClientIdAndroid => dotenv.env['GOOGLE_CLIENT_ID_ANDROID'] ?? '';
  // mqtt
  static String get mqttBrokerHost => dotenv.env['MQTT_BROKER_HOST'] ?? 'localhost';
  static int get mqttBrokerPort => int.tryParse(dotenv.env['MQTT_BROKER_PORT'] ?? '1883') ?? 1883;
  static int get mqttBrokerPortSSL => int.tryParse(dotenv.env['MQTT_BROKER_PORT_SSL'] ?? '8883') ?? 8883;
  static bool get mqttUseSSL => dotenv.env['MQTT_USE_SSL']?.toLowerCase() == 'true';
  // shopify
  static String get shopifyAccessToken =>
      dotenv.env['SHOPIFY_ACCESS_TOKEN'] ?? '';
  static String get shopifyStore => dotenv.env['SHOPIFY_STORE'] ?? '';
  // gomama verify
  static int get maxSelfieFailTries =>
      int.parse(dotenv.env['MAX_VERIFY_SELFIE_FAIL_TRIES'].toString());
  static String get selfieVerifyApiUrl =>
      dotenv.env['SELFIE_VERIFY_API_URL'] ?? '';
  static String get selfieVerifyApiKey =>
      dotenv.env['SELFIE_VERIFY_API_KEY'] ?? '';
  static String get firebase => dotenv.env['FIREBASE'] ?? '';
}
