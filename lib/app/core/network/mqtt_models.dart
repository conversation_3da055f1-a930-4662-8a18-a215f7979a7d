import 'package:freezed_annotation/freezed_annotation.dart';

part 'mqtt_models.freezed.dart';
part 'mqtt_models.g.dart';

@freezed
class ListingStatusUpdate with _$ListingStatusUpdate {
  const factory ListingStatusUpdate({
    required String listingId,
    required String status,
    required DateTime timestamp,
    Map<String, dynamic>? additionalData,
  }) = _ListingStatusUpdate;

  factory ListingStatusUpdate.fromJson(Map<String, dynamic> json) =>
      _$ListingStatusUpdateFromJson(json);

  factory ListingStatusUpdate.fromMqttMessage(Map<String, dynamic> message) {
    final data = message['data'] as Map<String, dynamic>;
    return ListingStatusUpdate(
      listingId: data['listing_id'] as String,
      status: data['status'] as String,
      timestamp: DateTime.fromMillisecondsSinceEpoch(
        message['timestamp'] as int,
      ),
      additionalData: data,
    );
  }
}

@freezed
class SessionUpdate with _$SessionUpdate {
  const factory SessionUpdate({
    required String type,
    required String sessionId,
    required Map<String, dynamic> data,
    required DateTime timestamp,
    String? userId,
  }) = _SessionUpdate;

  factory SessionUpdate.fromJson(Map<String, dynamic> json) =>
      _$SessionUpdateFromJson(json);

  factory SessionUpdate.fromMqttMessage(Map<String, dynamic> message) {
    final data = message['data'] as Map<String, dynamic>;
    return SessionUpdate(
      type: message['type'] as String,
      sessionId: data['session_id'] as String,
      data: data,
      timestamp: DateTime.fromMillisecondsSinceEpoch(
        message['timestamp'] as int,
      ),
      userId: data['user_id'] as String?,
    );
  }
}

@freezed
class UserNotification with _$UserNotification {
  const factory UserNotification({
    required String type,
    required String title,
    required String body,
    Map<String, dynamic>? data,
    required DateTime timestamp,
    String? userId,
  }) = _UserNotification;

  factory UserNotification.fromJson(Map<String, dynamic> json) =>
      _$UserNotificationFromJson(json);

  factory UserNotification.fromMqttMessage(Map<String, dynamic> message) {
    final data = message['data'] as Map<String, dynamic>;
    return UserNotification(
      type: message['type'] as String,
      title: data['title'] as String? ?? '',
      body: data['body'] as String? ?? '',
      data: data,
      timestamp: DateTime.fromMillisecondsSinceEpoch(
        message['timestamp'] as int,
      ),
      userId: data['user_id'] as String?,
    );
  }
}

@freezed
class MqttConnectionState with _$MqttConnectionState {
  const factory MqttConnectionState({
    required MqttConnectionStatus status,
    String? error,
    DateTime? lastConnected,
    int? reconnectAttempts,
  }) = _MqttConnectionState;

  factory MqttConnectionState.fromJson(Map<String, dynamic> json) =>
      _$MqttConnectionStateFromJson(json);
}

enum MqttConnectionStatus {
  connecting,
  connected,
  disconnected,
  reconnecting,
}
