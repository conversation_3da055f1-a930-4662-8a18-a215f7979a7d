// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'mqtt_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ListingStatusUpdate _$ListingStatusUpdateFromJson(Map<String, dynamic> json) {
  return _ListingStatusUpdate.fromJson(json);
}

/// @nodoc
mixin _$ListingStatusUpdate {
  String get listingId => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;
  Map<String, dynamic>? get additionalData =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ListingStatusUpdateCopyWith<ListingStatusUpdate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ListingStatusUpdateCopyWith<$Res> {
  factory $ListingStatusUpdateCopyWith(
          ListingStatusUpdate value, $Res Function(ListingStatusUpdate) then) =
      _$ListingStatusUpdateCopyWithImpl<$Res, ListingStatusUpdate>;
  @useResult
  $Res call(
      {String listingId,
      String status,
      DateTime timestamp,
      Map<String, dynamic>? additionalData});
}

/// @nodoc
class _$ListingStatusUpdateCopyWithImpl<$Res, $Val extends ListingStatusUpdate>
    implements $ListingStatusUpdateCopyWith<$Res> {
  _$ListingStatusUpdateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listingId = null,
    Object? status = null,
    Object? timestamp = null,
    Object? additionalData = freezed,
  }) {
    return _then(_value.copyWith(
      listingId: null == listingId
          ? _value.listingId
          : listingId // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      additionalData: freezed == additionalData
          ? _value.additionalData
          : additionalData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ListingStatusUpdateImplCopyWith<$Res>
    implements $ListingStatusUpdateCopyWith<$Res> {
  factory _$$ListingStatusUpdateImplCopyWith(_$ListingStatusUpdateImpl value,
          $Res Function(_$ListingStatusUpdateImpl) then) =
      __$$ListingStatusUpdateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String listingId,
      String status,
      DateTime timestamp,
      Map<String, dynamic>? additionalData});
}

/// @nodoc
class __$$ListingStatusUpdateImplCopyWithImpl<$Res>
    extends _$ListingStatusUpdateCopyWithImpl<$Res, _$ListingStatusUpdateImpl>
    implements _$$ListingStatusUpdateImplCopyWith<$Res> {
  __$$ListingStatusUpdateImplCopyWithImpl(_$ListingStatusUpdateImpl _value,
      $Res Function(_$ListingStatusUpdateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listingId = null,
    Object? status = null,
    Object? timestamp = null,
    Object? additionalData = freezed,
  }) {
    return _then(_$ListingStatusUpdateImpl(
      listingId: null == listingId
          ? _value.listingId
          : listingId // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      additionalData: freezed == additionalData
          ? _value._additionalData
          : additionalData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ListingStatusUpdateImpl implements _ListingStatusUpdate {
  const _$ListingStatusUpdateImpl(
      {required this.listingId,
      required this.status,
      required this.timestamp,
      final Map<String, dynamic>? additionalData})
      : _additionalData = additionalData;

  factory _$ListingStatusUpdateImpl.fromJson(Map<String, dynamic> json) =>
      _$$ListingStatusUpdateImplFromJson(json);

  @override
  final String listingId;
  @override
  final String status;
  @override
  final DateTime timestamp;
  final Map<String, dynamic>? _additionalData;
  @override
  Map<String, dynamic>? get additionalData {
    final value = _additionalData;
    if (value == null) return null;
    if (_additionalData is EqualUnmodifiableMapView) return _additionalData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'ListingStatusUpdate(listingId: $listingId, status: $status, timestamp: $timestamp, additionalData: $additionalData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListingStatusUpdateImpl &&
            (identical(other.listingId, listingId) ||
                other.listingId == listingId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            const DeepCollectionEquality()
                .equals(other._additionalData, _additionalData));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, listingId, status, timestamp,
      const DeepCollectionEquality().hash(_additionalData));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ListingStatusUpdateImplCopyWith<_$ListingStatusUpdateImpl> get copyWith =>
      __$$ListingStatusUpdateImplCopyWithImpl<_$ListingStatusUpdateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ListingStatusUpdateImplToJson(
      this,
    );
  }
}

abstract class _ListingStatusUpdate implements ListingStatusUpdate {
  const factory _ListingStatusUpdate(
      {required final String listingId,
      required final String status,
      required final DateTime timestamp,
      final Map<String, dynamic>? additionalData}) = _$ListingStatusUpdateImpl;

  factory _ListingStatusUpdate.fromJson(Map<String, dynamic> json) =
      _$ListingStatusUpdateImpl.fromJson;

  @override
  String get listingId;
  @override
  String get status;
  @override
  DateTime get timestamp;
  @override
  Map<String, dynamic>? get additionalData;
  @override
  @JsonKey(ignore: true)
  _$$ListingStatusUpdateImplCopyWith<_$ListingStatusUpdateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SessionUpdate _$SessionUpdateFromJson(Map<String, dynamic> json) {
  return _SessionUpdate.fromJson(json);
}

/// @nodoc
mixin _$SessionUpdate {
  String get type => throw _privateConstructorUsedError;
  String get sessionId => throw _privateConstructorUsedError;
  Map<String, dynamic> get data => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;
  String? get userId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SessionUpdateCopyWith<SessionUpdate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SessionUpdateCopyWith<$Res> {
  factory $SessionUpdateCopyWith(
          SessionUpdate value, $Res Function(SessionUpdate) then) =
      _$SessionUpdateCopyWithImpl<$Res, SessionUpdate>;
  @useResult
  $Res call(
      {String type,
      String sessionId,
      Map<String, dynamic> data,
      DateTime timestamp,
      String? userId});
}

/// @nodoc
class _$SessionUpdateCopyWithImpl<$Res, $Val extends SessionUpdate>
    implements $SessionUpdateCopyWith<$Res> {
  _$SessionUpdateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? sessionId = null,
    Object? data = null,
    Object? timestamp = null,
    Object? userId = freezed,
  }) {
    return _then(_value.copyWith(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SessionUpdateImplCopyWith<$Res>
    implements $SessionUpdateCopyWith<$Res> {
  factory _$$SessionUpdateImplCopyWith(
          _$SessionUpdateImpl value, $Res Function(_$SessionUpdateImpl) then) =
      __$$SessionUpdateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String type,
      String sessionId,
      Map<String, dynamic> data,
      DateTime timestamp,
      String? userId});
}

/// @nodoc
class __$$SessionUpdateImplCopyWithImpl<$Res>
    extends _$SessionUpdateCopyWithImpl<$Res, _$SessionUpdateImpl>
    implements _$$SessionUpdateImplCopyWith<$Res> {
  __$$SessionUpdateImplCopyWithImpl(
      _$SessionUpdateImpl _value, $Res Function(_$SessionUpdateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? sessionId = null,
    Object? data = null,
    Object? timestamp = null,
    Object? userId = freezed,
  }) {
    return _then(_$SessionUpdateImpl(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SessionUpdateImpl implements _SessionUpdate {
  const _$SessionUpdateImpl(
      {required this.type,
      required this.sessionId,
      required final Map<String, dynamic> data,
      required this.timestamp,
      this.userId})
      : _data = data;

  factory _$SessionUpdateImpl.fromJson(Map<String, dynamic> json) =>
      _$$SessionUpdateImplFromJson(json);

  @override
  final String type;
  @override
  final String sessionId;
  final Map<String, dynamic> _data;
  @override
  Map<String, dynamic> get data {
    if (_data is EqualUnmodifiableMapView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_data);
  }

  @override
  final DateTime timestamp;
  @override
  final String? userId;

  @override
  String toString() {
    return 'SessionUpdate(type: $type, sessionId: $sessionId, data: $data, timestamp: $timestamp, userId: $userId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SessionUpdateImpl &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, type, sessionId,
      const DeepCollectionEquality().hash(_data), timestamp, userId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SessionUpdateImplCopyWith<_$SessionUpdateImpl> get copyWith =>
      __$$SessionUpdateImplCopyWithImpl<_$SessionUpdateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SessionUpdateImplToJson(
      this,
    );
  }
}

abstract class _SessionUpdate implements SessionUpdate {
  const factory _SessionUpdate(
      {required final String type,
      required final String sessionId,
      required final Map<String, dynamic> data,
      required final DateTime timestamp,
      final String? userId}) = _$SessionUpdateImpl;

  factory _SessionUpdate.fromJson(Map<String, dynamic> json) =
      _$SessionUpdateImpl.fromJson;

  @override
  String get type;
  @override
  String get sessionId;
  @override
  Map<String, dynamic> get data;
  @override
  DateTime get timestamp;
  @override
  String? get userId;
  @override
  @JsonKey(ignore: true)
  _$$SessionUpdateImplCopyWith<_$SessionUpdateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserNotification _$UserNotificationFromJson(Map<String, dynamic> json) {
  return _UserNotification.fromJson(json);
}

/// @nodoc
mixin _$UserNotification {
  String get type => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get body => throw _privateConstructorUsedError;
  Map<String, dynamic>? get data => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;
  String? get userId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserNotificationCopyWith<UserNotification> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserNotificationCopyWith<$Res> {
  factory $UserNotificationCopyWith(
          UserNotification value, $Res Function(UserNotification) then) =
      _$UserNotificationCopyWithImpl<$Res, UserNotification>;
  @useResult
  $Res call(
      {String type,
      String title,
      String body,
      Map<String, dynamic>? data,
      DateTime timestamp,
      String? userId});
}

/// @nodoc
class _$UserNotificationCopyWithImpl<$Res, $Val extends UserNotification>
    implements $UserNotificationCopyWith<$Res> {
  _$UserNotificationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? title = null,
    Object? body = null,
    Object? data = freezed,
    Object? timestamp = null,
    Object? userId = freezed,
  }) {
    return _then(_value.copyWith(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      body: null == body
          ? _value.body
          : body // ignore: cast_nullable_to_non_nullable
              as String,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserNotificationImplCopyWith<$Res>
    implements $UserNotificationCopyWith<$Res> {
  factory _$$UserNotificationImplCopyWith(_$UserNotificationImpl value,
          $Res Function(_$UserNotificationImpl) then) =
      __$$UserNotificationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String type,
      String title,
      String body,
      Map<String, dynamic>? data,
      DateTime timestamp,
      String? userId});
}

/// @nodoc
class __$$UserNotificationImplCopyWithImpl<$Res>
    extends _$UserNotificationCopyWithImpl<$Res, _$UserNotificationImpl>
    implements _$$UserNotificationImplCopyWith<$Res> {
  __$$UserNotificationImplCopyWithImpl(_$UserNotificationImpl _value,
      $Res Function(_$UserNotificationImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? title = null,
    Object? body = null,
    Object? data = freezed,
    Object? timestamp = null,
    Object? userId = freezed,
  }) {
    return _then(_$UserNotificationImpl(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      body: null == body
          ? _value.body
          : body // ignore: cast_nullable_to_non_nullable
              as String,
      data: freezed == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserNotificationImpl implements _UserNotification {
  const _$UserNotificationImpl(
      {required this.type,
      required this.title,
      required this.body,
      final Map<String, dynamic>? data,
      required this.timestamp,
      this.userId})
      : _data = data;

  factory _$UserNotificationImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserNotificationImplFromJson(json);

  @override
  final String type;
  @override
  final String title;
  @override
  final String body;
  final Map<String, dynamic>? _data;
  @override
  Map<String, dynamic>? get data {
    final value = _data;
    if (value == null) return null;
    if (_data is EqualUnmodifiableMapView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final DateTime timestamp;
  @override
  final String? userId;

  @override
  String toString() {
    return 'UserNotification(type: $type, title: $title, body: $body, data: $data, timestamp: $timestamp, userId: $userId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserNotificationImpl &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.body, body) || other.body == body) &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, type, title, body,
      const DeepCollectionEquality().hash(_data), timestamp, userId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UserNotificationImplCopyWith<_$UserNotificationImpl> get copyWith =>
      __$$UserNotificationImplCopyWithImpl<_$UserNotificationImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserNotificationImplToJson(
      this,
    );
  }
}

abstract class _UserNotification implements UserNotification {
  const factory _UserNotification(
      {required final String type,
      required final String title,
      required final String body,
      final Map<String, dynamic>? data,
      required final DateTime timestamp,
      final String? userId}) = _$UserNotificationImpl;

  factory _UserNotification.fromJson(Map<String, dynamic> json) =
      _$UserNotificationImpl.fromJson;

  @override
  String get type;
  @override
  String get title;
  @override
  String get body;
  @override
  Map<String, dynamic>? get data;
  @override
  DateTime get timestamp;
  @override
  String? get userId;
  @override
  @JsonKey(ignore: true)
  _$$UserNotificationImplCopyWith<_$UserNotificationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

MqttConnectionState _$MqttConnectionStateFromJson(Map<String, dynamic> json) {
  return _MqttConnectionState.fromJson(json);
}

/// @nodoc
mixin _$MqttConnectionState {
  MqttConnectionStatus get status => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  DateTime? get lastConnected => throw _privateConstructorUsedError;
  int? get reconnectAttempts => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MqttConnectionStateCopyWith<MqttConnectionState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MqttConnectionStateCopyWith<$Res> {
  factory $MqttConnectionStateCopyWith(
          MqttConnectionState value, $Res Function(MqttConnectionState) then) =
      _$MqttConnectionStateCopyWithImpl<$Res, MqttConnectionState>;
  @useResult
  $Res call(
      {MqttConnectionStatus status,
      String? error,
      DateTime? lastConnected,
      int? reconnectAttempts});
}

/// @nodoc
class _$MqttConnectionStateCopyWithImpl<$Res, $Val extends MqttConnectionState>
    implements $MqttConnectionStateCopyWith<$Res> {
  _$MqttConnectionStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? error = freezed,
    Object? lastConnected = freezed,
    Object? reconnectAttempts = freezed,
  }) {
    return _then(_value.copyWith(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as MqttConnectionStatus,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      lastConnected: freezed == lastConnected
          ? _value.lastConnected
          : lastConnected // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      reconnectAttempts: freezed == reconnectAttempts
          ? _value.reconnectAttempts
          : reconnectAttempts // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MqttConnectionStateImplCopyWith<$Res>
    implements $MqttConnectionStateCopyWith<$Res> {
  factory _$$MqttConnectionStateImplCopyWith(_$MqttConnectionStateImpl value,
          $Res Function(_$MqttConnectionStateImpl) then) =
      __$$MqttConnectionStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {MqttConnectionStatus status,
      String? error,
      DateTime? lastConnected,
      int? reconnectAttempts});
}

/// @nodoc
class __$$MqttConnectionStateImplCopyWithImpl<$Res>
    extends _$MqttConnectionStateCopyWithImpl<$Res, _$MqttConnectionStateImpl>
    implements _$$MqttConnectionStateImplCopyWith<$Res> {
  __$$MqttConnectionStateImplCopyWithImpl(_$MqttConnectionStateImpl _value,
      $Res Function(_$MqttConnectionStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? error = freezed,
    Object? lastConnected = freezed,
    Object? reconnectAttempts = freezed,
  }) {
    return _then(_$MqttConnectionStateImpl(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as MqttConnectionStatus,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      lastConnected: freezed == lastConnected
          ? _value.lastConnected
          : lastConnected // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      reconnectAttempts: freezed == reconnectAttempts
          ? _value.reconnectAttempts
          : reconnectAttempts // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MqttConnectionStateImpl implements _MqttConnectionState {
  const _$MqttConnectionStateImpl(
      {required this.status,
      this.error,
      this.lastConnected,
      this.reconnectAttempts});

  factory _$MqttConnectionStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$MqttConnectionStateImplFromJson(json);

  @override
  final MqttConnectionStatus status;
  @override
  final String? error;
  @override
  final DateTime? lastConnected;
  @override
  final int? reconnectAttempts;

  @override
  String toString() {
    return 'MqttConnectionState(status: $status, error: $error, lastConnected: $lastConnected, reconnectAttempts: $reconnectAttempts)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MqttConnectionStateImpl &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.lastConnected, lastConnected) ||
                other.lastConnected == lastConnected) &&
            (identical(other.reconnectAttempts, reconnectAttempts) ||
                other.reconnectAttempts == reconnectAttempts));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, status, error, lastConnected, reconnectAttempts);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MqttConnectionStateImplCopyWith<_$MqttConnectionStateImpl> get copyWith =>
      __$$MqttConnectionStateImplCopyWithImpl<_$MqttConnectionStateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MqttConnectionStateImplToJson(
      this,
    );
  }
}

abstract class _MqttConnectionState implements MqttConnectionState {
  const factory _MqttConnectionState(
      {required final MqttConnectionStatus status,
      final String? error,
      final DateTime? lastConnected,
      final int? reconnectAttempts}) = _$MqttConnectionStateImpl;

  factory _MqttConnectionState.fromJson(Map<String, dynamic> json) =
      _$MqttConnectionStateImpl.fromJson;

  @override
  MqttConnectionStatus get status;
  @override
  String? get error;
  @override
  DateTime? get lastConnected;
  @override
  int? get reconnectAttempts;
  @override
  @JsonKey(ignore: true)
  _$$MqttConnectionStateImplCopyWith<_$MqttConnectionStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
