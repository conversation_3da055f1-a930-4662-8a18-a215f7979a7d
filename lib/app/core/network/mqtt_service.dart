import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:gomama/app/core/constants/environment.dart';
import 'package:gomama/app/core/network/mqtt_models.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:groveman/groveman.dart';
import 'package:mqtt_client/mqtt_client.dart' as mqtt;
import 'package:mqtt_client/mqtt_server_client.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'mqtt_service.g.dart';

@Riverpod(keepAlive: true)
MqttService mqttService(MqttServiceRef ref) {
  final realtimeJwt = ref.watch(
    authControllerProvider.select(
      (auth) => auth.hasValue ? auth.requireValue.realtimeJwt : null,
    ),
  );
  final userId = ref.watch(
    authControllerProvider.select(
      (auth) => auth.hasValue ? auth.requireValue.id : null,
    ),
  );

  Groveman.info(
    'mqttServiceProvider rebuilt. Auth state: $realtimeJwt, $userId',
  );

  if (realtimeJwt == null || userId == null) {
    return MqttService(
      getToken: () async => null,
      clientId: 'uninitialized',
    );
  }

  final service = MqttService(
    getToken: () async => realtimeJwt,
    clientId: userId,
  )..connect();

  return service;
}

class MqttService {
  MqttService({
    required this.getToken,
    required this.clientId,
  }) {
    _initializeClient();
  }

  final Future<String?> Function() getToken;
  final String clientId;

  MqttServerClient? _client;
  final StreamController<Map<String, dynamic>> _messageController =
      StreamController.broadcast();
  final StreamController<MqttConnectionStatus> _statusController =
      StreamController.broadcast();

  // Typed stream controllers
  final StreamController<ListingStatusUpdate> _listingStatusController =
      StreamController.broadcast();
  // final StreamController<SessionUpdate> _sessionUpdateController =
  //     StreamController.broadcast();
  final StreamController<UserNotification> _notificationController =
      StreamController.broadcast();
  Timer? _reconnectTimer;
  int _reconnectAttempts = 0;
  final Set<String> _subscribedTopics = <String>{};

  Stream<Map<String, dynamic>> get messages => _messageController.stream;
  Stream<MqttConnectionStatus> get status => _statusController.stream;

  // Typed streams
  Stream<ListingStatusUpdate> get listingStatusStream =>
      _listingStatusController.stream;
  // Stream<SessionUpdate> get sessionUpdateStream =>
  //     _sessionUpdateController.stream;
  Stream<UserNotification> get notificationStream =>
      _notificationController.stream;

  StreamSubscription? _updatesSubscription;

  void _initializeClient() {
    _client = MqttServerClient.withPort(
      Environment.mqttBrokerHost,
      clientId,
      Environment.mqttUseSSL
          ? Environment.mqttBrokerPortSSL
          : Environment.mqttBrokerPort,
    );

    // Configure SSL if enabled
    if (Environment.mqttUseSSL) {
      _client!.secure = true;
      _client!.securityContext = SecurityContext.defaultContext;
    }

    _client!.keepAlivePeriod = 60;
    _client!.autoReconnect = true;
    _client!.resubscribeOnAutoReconnect = true;
    _client!.logging(on: false);

    // Set up connection callbacks
    _client!.onConnected = _onConnected;
    _client!.onDisconnected = _onDisconnected;
    _client!.onAutoReconnect = _onAutoReconnect;
    _client!.onAutoReconnected = _onAutoReconnected;
  }

  Future<void> connect() async {
    if (_client == null) {
      _initializeClient();
    }

    _statusController.add(MqttConnectionStatus.connecting);
    Groveman.info('Attempting to connect to MQTT broker...');

    try {
      final token = await getToken();
      if (token == null) {
        Groveman.warning(
          'No authentication token available. Cannot connect to MQTT broker.',
        );
        _statusController.add(MqttConnectionStatus.disconnected);
        return;
      }

      // Set authentication credentials
      _client!.connectionMessage = mqtt.MqttConnectMessage()
          .withClientIdentifier(clientId)
          .authenticateAs(clientId, token)
          .withWillTopic('gomama/clients/$clientId/status')
          .withWillMessage('offline')
          .withWillQos(mqtt.MqttQos.atLeastOnce)
          .withWillRetain()
          .startClean();

      await _client!.connect();
    } catch (e) {
      Groveman.error('MQTT connection failed: $e. Attempting to reconnect...');
      _statusController.add(MqttConnectionStatus.reconnecting);
      _scheduleReconnect();
    }
  }

  void _onConnected() {
    _statusController.add(MqttConnectionStatus.connected);
    Groveman.info('MQTT connected.');
    _reconnectAttempts = 0; // Reset reconnect attempts on successful connection

    // Resubscribe to all previously subscribed topics
    for (final topic in _subscribedTopics) {
      _subscribeToTopic(topic);
    }

    // Publish online status
    _publishMessage(
      'gomama/clients/$clientId/status',
      'online',
      retain: true,
    );

    // Set up message subscription callback
    _updatesSubscription?.cancel(); // Prevent multiple listeners
    _updatesSubscription = _client!.updates!.listen(_onMessageReceived);
  }

  void _onDisconnected() {
    Groveman.warning('MQTT disconnected. Attempting to reconnect...');
    _statusController.add(MqttConnectionStatus.reconnecting);
    _updatesSubscription?.cancel();
    _updatesSubscription = null;

    _scheduleReconnect();
  }

  void _onAutoReconnect() {
    Groveman.info('MQTT auto-reconnecting...');
    _statusController.add(MqttConnectionStatus.reconnecting);
  }

  void _onAutoReconnected() {
    Groveman.info('MQTT auto-reconnected.');
    _statusController.add(MqttConnectionStatus.connected);
    _reconnectAttempts = 0;
  }

  void _onMessageReceived(
    List<mqtt.MqttReceivedMessage<mqtt.MqttMessage>> messages,
  ) {
    for (final message in messages) {
      final topic = message.topic;
      final payload = mqtt.MqttPublishPayload.bytesToStringAsString(
        (message.payload as mqtt.MqttPublishMessage).payload.message,
      );

      try {
        final decodedMessage = jsonDecode(payload) as Map<String, dynamic>;
        // Add topic to the message for compatibility with existing code
        decodedMessage['topic'] = topic;
        _messageController.add(decodedMessage);

        // Route to typed streams
        _routeMessage(topic, decodedMessage);

        Groveman.debug('MQTT message received on topic $topic: $payload');
      } catch (e) {
        Groveman.error('Failed to decode MQTT message: $e');
      }
    }
  }

  /// Route messages to appropriate typed streams
  void _routeMessage(String topic, Map<String, dynamic> data) {
    try {
      if (topic.startsWith('gomama/listings/status/')) {
        final update = ListingStatusUpdate.fromMqttMessage(data);
        _listingStatusController.add(update);
      } else if (topic.startsWith('gomama/users/notifications/')) {
        // Ignore empty messages
        // triggered from acknowledge & clearing of retained messages
        if (data['data'] == null || data['data'] == '') {
          return;
        }

        final notification = UserNotification.fromMqttMessage(data);
        _notificationController.add(notification);
      }
      // else if (topic.startsWith('gomama/users/sessions/')) {
      //   final update = SessionUpdate.fromMqttMessage(data);
      //   _sessionUpdateController.add(update);
      // }
    } catch (e) {
      Groveman.error('Failed to route MQTT message from topic $topic: $e');
    }
  }

  void _scheduleReconnect() {
    _reconnectTimer?.cancel();
    final delay = Duration(seconds: _getReconnectDelay());
    _reconnectTimer = Timer(delay, () {
      _reconnectAttempts++;
      connect();
    });
  }

  int _getReconnectDelay() {
    // Exponential backoff with a cap
    return (1 << _reconnectAttempts)
        .clamp(1, 60); // 1, 2, 4, 8, 16, 32, 60 seconds max
  }

  void subscribeToTopic(String topic) {
    _subscribedTopics.add(topic);
    if (_client?.connectionStatus?.state ==
        mqtt.MqttConnectionState.connected) {
      Groveman.info('Subscribed to MQTT topic: $topic');
      _subscribeToTopic(topic);
    }
  }

  void _subscribeToTopic(String topic) {
    _client?.subscribe(topic, mqtt.MqttQos.atLeastOnce);
  }

  void unsubscribeFromTopic(String topic) {
    _subscribedTopics.remove(topic);
    if (_client?.connectionStatus?.state ==
        mqtt.MqttConnectionState.connected) {
      Groveman.info('Unsubscribed from MQTT topic: $topic');
      _client?.unsubscribe(topic);
    }
  }

  void publishMessage(String topic, Map<String, dynamic> data) {
    final message = {
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'source': 'flutter',
      'data': data,
    };

    _publishMessage(topic, jsonEncode(message));
  }

  void _publishMessage(
    String topic,
    String message, {
    mqtt.MqttQos qos = mqtt.MqttQos.atLeastOnce,
    bool retain = false,
  }) {
    if (_client?.connectionStatus?.state ==
        mqtt.MqttConnectionState.connected) {
      final builder = mqtt.MqttClientPayloadBuilder()..addString(message);
      _client!.publishMessage(topic, qos, builder.payload!, retain: retain);
      Groveman.debug('MQTT message published to topic $topic: $message');
    } else {
      Groveman.warning(
        'MQTT not connected. Cannot publish message to topic: $topic',
      );
    }
  }

  // Topic-specific subscription methods
  // void subscribeToUserSessions(String userId) {
  //   final topic = 'gomama/users/sessions/$userId';
  //   subscribeToTopic(topic);
  //   Groveman.info('Subscribed to user sessions: $userId');
  // }

  void subscribeToUserNotifications(String userId) {
    final topic = 'gomama/users/notifications/$userId';
    subscribeToTopic(topic);
    Groveman.info('Subscribed to user notifications: $userId');
  }

  // TODO: update listing availability & status live in the list view & map (if possible)
  void subscribeToListingStatus(String listingId) {
    final topic = 'gomama/listings/status/$listingId';
    subscribeToTopic(topic);
    Groveman.info('Subscribed to listing status: $listingId');
  }

  // TODO: update listing availability & status live in the list view & map (if possible)
  void subscribeToListingAvailability(String listingId) {
    final topic = 'gomama/listings/availability/$listingId';
    subscribeToTopic(topic);
    Groveman.info('Subscribed to listing availability: $listingId');
  }

  void unsubscribeFromUserSessions(String userId) {
    final topic = 'gomama/users/sessions/$userId';
    unsubscribeFromTopic(topic);
    Groveman.info('Unsubscribed from user sessions: $userId');
  }

  void unsubscribeFromUserNotifications(String userId) {
    final topic = 'gomama/users/notifications/$userId';
    unsubscribeFromTopic(topic);
    Groveman.info('Unsubscribed from user notifications: $userId');
  }

  void unsubscribeFromListingStatus(String listingId) {
    final topic = 'gomama/listings/status/$listingId';
    unsubscribeFromTopic(topic);
    Groveman.info('Unsubscribed from listing status: $listingId');
  }

  void unsubscribeFromListingAvailability(String listingId) {
    final topic = 'gomama/listings/availability/$listingId';
    unsubscribeFromTopic(topic);
    Groveman.info('Unsubscribed from listing availability: $listingId');
  }

  void dispose() {
    _reconnectTimer?.cancel();

    // Publish offline status before disconnecting
    if (_client?.connectionStatus?.state ==
        mqtt.MqttConnectionState.connected) {
      _publishMessage(
        'gomama/clients/$clientId/status',
        'offline',
        retain: true,
      );
    }

    _client?.disconnect();
    _messageController.close();
    _statusController.close();
    _listingStatusController.close();
    // _sessionUpdateController.close();
    _notificationController.close();
    Groveman.info('MqttService disposed.');
  }
}
