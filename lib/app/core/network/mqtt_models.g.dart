// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mqtt_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ListingStatusUpdateImpl _$$ListingStatusUpdateImplFromJson(
        Map<String, dynamic> json) =>
    _$ListingStatusUpdateImpl(
      listingId: json['listing_id'] as String,
      status: json['status'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      additionalData: json['additional_data'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$ListingStatusUpdateImplToJson(
        _$ListingStatusUpdateImpl instance) =>
    <String, dynamic>{
      'listing_id': instance.listingId,
      'status': instance.status,
      'timestamp': instance.timestamp.toIso8601String(),
      'additional_data': instance.additionalData,
    };

_$SessionUpdateImpl _$$SessionUpdateImplFromJson(Map<String, dynamic> json) =>
    _$SessionUpdateImpl(
      type: json['type'] as String,
      sessionId: json['session_id'] as String,
      data: json['data'] as Map<String, dynamic>,
      timestamp: DateTime.parse(json['timestamp'] as String),
      userId: json['user_id'] as String?,
    );

Map<String, dynamic> _$$SessionUpdateImplToJson(_$SessionUpdateImpl instance) =>
    <String, dynamic>{
      'type': instance.type,
      'session_id': instance.sessionId,
      'data': instance.data,
      'timestamp': instance.timestamp.toIso8601String(),
      'user_id': instance.userId,
    };

_$UserNotificationImpl _$$UserNotificationImplFromJson(
        Map<String, dynamic> json) =>
    _$UserNotificationImpl(
      type: json['type'] as String,
      title: json['title'] as String,
      body: json['body'] as String,
      data: json['data'] as Map<String, dynamic>?,
      timestamp: DateTime.parse(json['timestamp'] as String),
      userId: json['user_id'] as String?,
    );

Map<String, dynamic> _$$UserNotificationImplToJson(
        _$UserNotificationImpl instance) =>
    <String, dynamic>{
      'type': instance.type,
      'title': instance.title,
      'body': instance.body,
      'data': instance.data,
      'timestamp': instance.timestamp.toIso8601String(),
      'user_id': instance.userId,
    };

_$MqttConnectionStateImpl _$$MqttConnectionStateImplFromJson(
        Map<String, dynamic> json) =>
    _$MqttConnectionStateImpl(
      status: $enumDecode(_$MqttConnectionStatusEnumMap, json['status']),
      error: json['error'] as String?,
      lastConnected: json['last_connected'] == null
          ? null
          : DateTime.parse(json['last_connected'] as String),
      reconnectAttempts: (json['reconnect_attempts'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$MqttConnectionStateImplToJson(
        _$MqttConnectionStateImpl instance) =>
    <String, dynamic>{
      'status': _$MqttConnectionStatusEnumMap[instance.status]!,
      'error': instance.error,
      'last_connected': instance.lastConnected?.toIso8601String(),
      'reconnect_attempts': instance.reconnectAttempts,
    };

const _$MqttConnectionStatusEnumMap = {
  MqttConnectionStatus.connecting: 'connecting',
  MqttConnectionStatus.connected: 'connected',
  MqttConnectionStatus.disconnected: 'disconnected',
  MqttConnectionStatus.reconnecting: 'reconnecting',
};
