import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_branch_sdk/flutter_branch_sdk.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/notification/providers/device_providers.dart';
import 'package:gomama/app/core/notification/providers/fcm_providers.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/main/provider/permission_providers.dart';
import 'package:gomama/app/features/main/view/splash_view.dart';
import 'package:gomama/app/features/singpass/provider/singpass_providers.dart';
import 'package:gomama/app/features/verification/provider/verification_providers.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'router.g.dart';

/// Exposes a [GoRouter] that uses a [Listenable] to refresh its internal state.
///
/// With Riverpod, we can't register a dependency via an Inherited Widget,
/// thus making this implementation the "leanest" possible
///
/// To sync our app state with this our router, we simply update our listenable via `ref.listen`,
/// and pass it to GoRouter's `refreshListenable`.
/// In this example, this will trigger redirects on any authentication change.
///
/// Obviously, more logic could be implemented here, but again, this is meant to be a simple example.
/// You can always build more listenables and even merge more than one into a more complex `ChangeNotifier`,
/// but that's up to your case and out of this scope.
final routerKey = GlobalKey<NavigatorState>(debugLabel: 'routerKey');

@riverpod
GoRouter router(RouterRef ref) {
  final isAuth = ValueNotifier<AsyncValue<bool>>(const AsyncLoading());
  final hasLocationPermission =
      ValueNotifier<AsyncValue<bool>>(const AsyncLoading());
  final hasNotificationPermission =
      ValueNotifier<AsyncValue<bool>>(const AsyncLoading());
  final isSplashReady = ValueNotifier<bool>(false);
  final deepLinkNotifier = ValueNotifier<String?>(null);
  // ignore: strict_raw_type
  StreamSubscription? branchSubscription;

  void handleDeepLink(Map<dynamic, dynamic> data) {
    // Extract deep link data
    final linkData = data['~referring_link'] as String?;
    if (linkData == null) return;

    debugPrint(data.toString());

    // Handle product deep links
    if (data.containsKey('productHandle')) {
      final productHandle = data['productHandle'] as String;
      deepLinkNotifier.value = '/commerce/products/$productHandle';
    }

    // Handle listing deep links
    if (data.containsKey('listingId')) {
      final listingId = data['listingId'] as String;
      deepLinkNotifier.value = '/listings/$listingId';
    }
  }

  // Initialize Branch SDK deep linking
  branchSubscription = FlutterBranchSdk.listSession().listen(
    (data) {
      if (data.containsKey('+clicked_branch_link') &&
          data['+clicked_branch_link'] == true) {
        handleDeepLink(data);
      }
    },
    onError: (error) {
      debugPrint('Deep Link Error: $error');
    },
  );

  // Get initial deep link if app was launched from a Branch link
  FlutterBranchSdk.getFirstReferringParams().then((initialData) {
    if (initialData.containsKey('+clicked_branch_link') &&
        initialData['+clicked_branch_link'] == true) {
      handleDeepLink(initialData);
    }
  });

  ref
    ..onDispose(() {
      isAuth.dispose();
      hasLocationPermission.dispose();
      hasNotificationPermission.dispose();
      isSplashReady.dispose();
      deepLinkNotifier.dispose();
      branchSubscription?.cancel();
    })
    // update the listenable, when some provider value changes
    // here, we are just interested in whether the user's logged in
    ..listen(
      authControllerProvider
          .select((value) => value.whenData((value) => value.isAuth)),
      (_, next) async {
        // if prev & next is the same, do nothing
        // if (next == isAuth.value.unwrapPrevious()) {
        //   return;
        // }

        isAuth.value = next;

        // create device when user becomes authenticated
        if (next.valueOrNull == true) {
          final fcmState = await ref.read(fcmControllerProvider.future);
          if (fcmState.fcmToken != null) {
            try {
              await ref.read(createDeviceProvider(fcmState.fcmToken!).future);
            } catch (e) {
              Groveman.error('Failed to create device', error: e);
            }
          }
        }
      },
    )
    ..listen(
      permissionsProvider,
      (_, next) {
        if (next.hasValue) {
          hasLocationPermission.value =
              AsyncData(next.value?.hasLocationPermission ?? false);
          hasNotificationPermission.value =
              AsyncData(next.value?.hasNotificationPermission ?? false);
        }
      },
    )
    ..listen(
      splashStateProvider.select((value) => value),
      (_, next) {
        isSplashReady.value = next;
      },
    );

  final routerListenable = Listenable.merge([
    isAuth,
    hasLocationPermission,
    hasNotificationPermission,
    isSplashReady,
    deepLinkNotifier,
  ]);

  final router = GoRouter(
    navigatorKey: routerKey,
    refreshListenable: routerListenable,
    initialLocation: const SplashRoute().location,
    debugLogDiagnostics: true,
    routes: $appRoutes,
    redirect: (context, state) async {
      if (isAuth.value.unwrapPrevious().hasError) {
        return const AuthRoute().location;
      }

      if (hasLocationPermission.value.isLoading ||
          !hasLocationPermission.value.hasValue ||
          hasNotificationPermission.value.isLoading ||
          !hasNotificationPermission.value.hasValue ||
          isAuth.value.isLoading ||
          !isAuth.value.hasValue) {
        return const SplashRoute().location;
      }

      final auth = isAuth.value.requireValue;
      // Location and notification permissions are now optional for app usage
      // They are still tracked for UI features that benefit from permissions
      final deepLinkPath = deepLinkNotifier.value;
      final isSplash = state.uri.path == const SplashRoute().location;

      // Location permission is now optional - users can access the app without it
      // The app will gracefully handle missing location by using default coordinates
      // No redirect to permission view - users can access the app without permissions

      // Notification permission is optional, no redirect needed

      if (isSplash) {
        if (isSplashReady.value) {
          // deeplink: for cold launch
          if (deepLinkPath != null) {
            final path = deepLinkPath;
            if (auth) {
              deepLinkNotifier.value =
                  null; // Clear the deep link after using it
              return path;
            } else {
              return const AuthRoute().location;
            }
          }
          return auth
              ? const ExploreRoute().location
              : const AuthRoute().location;
        }
        return const SplashRoute().location;
      }

      // handle singpass deep link
      if (state.uri.path == '/singpass/myinfo/callback') {
        final _code = state.uri.queryParameters['code'];
        final _state = state.uri.queryParameters['state'];

        if (_code != null && _state != null && _code != 'undefined') {
          final singpassResponse = await ref
              .read(processSingpassMyinfoProvider(_code, _state).future);

          // close the in app browser
          if (ref.read(singpassBrowserControllerProvider).isOpened()) {
            await ref.read(singpassBrowserControllerProvider).close();
          }

          // if is a men, send to state 22 (mother only, men not allowed)
          if (singpassResponse.gender?.toLowerCase() == 'male' ||
              singpassResponse.gender?.toLowerCase() == 'unknown') {
            ref
                .read(verificationFlowControllerProvider.notifier)
                .toSingpassError(22);
            return const VerificationRoute().location;
          }

          // if no children, send to state 23 (missing children)
          if (singpassResponse.childrenBirthRecords?.isEmpty == true) {
            ref
                .read(verificationFlowControllerProvider.notifier)
                .toSingpassError(23);
            return const VerificationRoute().location;
          }

          // Store the response in the provider for the form route
          ref.read(singpassResponseProvider.notifier).state = singpassResponse;

          // otherwise send to singpass form
          return const SingpassFormRoute().location;
        }
      }

      // deeplink: for redirecting running app
      // Handle deep link redirect for non-splash pages
      if (deepLinkPath != null) {
        final path = deepLinkPath;
        if (auth) {
          deepLinkNotifier.value = null; // Clear the deep link after using it
          return path;
        } else {
          return const AuthRoute().location;
        }
      }

      // Location permission is now optional - users can access the app without it
      // The app will gracefully handle missing location by using default coordinates
      // No redirect to permission view - users can access the app without permissions

      // Notification permission is optional, no redirect needed

      final isLoggingIn = state.uri.path == const AuthRoute().location;
      if (isLoggingIn) return auth ? const ExploreRoute().location : null;

      // if auth routes (e.g: otp)
      if (state.uri.path.startsWith('/auth')) {
        return auth ? const SplashRoute().location : null;
      }

      return auth ? null : const SplashRoute().location;
    },
  );

  ref.onDispose(router.dispose); // always clean up after yourselves (:

  return router;
}
