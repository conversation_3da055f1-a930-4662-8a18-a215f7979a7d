import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

class UnlockSessionDemoVideoView extends StatefulWidget {
  const UnlockSessionDemoVideoView({super.key});

  @override
  State<UnlockSessionDemoVideoView> createState() =>
      _UnlockSessionDemoVideoViewState();
}

class _UnlockSessionDemoVideoViewState
    extends State<UnlockSessionDemoVideoView> {
  late VideoPlayerController _videoPlayerController;
  late Future<void> _initializeVideoPlayerFuture;

  @override
  void initState() {
    // Note: was misspelled as "initstate"
    super.initState();

    _videoPlayerController =
        VideoPlayerController.asset('assets/videos/unlock_guide.mp4')
          ..setLooping(true) // Add looping if needed
          ..setVolume(1); // Set volume if needed

    _initializeVideoPlayerFuture =
        _videoPlayerController.initialize().then((_) {
      // Start playing once initialized
      _videoPlayerController.play();
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _videoPlayerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: _initializeVideoPlayerFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          return AspectRatio(
            aspectRatio: 4 / 3,
            child: ClipRect(
              child: OverflowBox(
                maxHeight: double.infinity,
                child: FittedBox(
                  fit: BoxFit.cover,
                  child: SizedBox(
                    // width: _videoPlayerController.value.size.width,
                    // height: _videoPlayerController.value.size.height,
                    width: 1080, height: 1080,
                    child: VideoPlayer(_videoPlayerController),
                  ),
                ),
              ),
            ),
          );
        } else {
          return const AspectRatio(
            aspectRatio: 4 / 3,
            child: SizedBox.shrink(),
          );
        }
      },
    );
  }
}
