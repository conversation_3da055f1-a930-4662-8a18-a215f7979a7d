import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/session/provider/session_providers.dart';
import 'package:gomama/app/features/session/view/current_session_view.dart';
import 'package:gomama/app/features/session/view/review_session_view.dart';
import 'package:gomama/app/widgets/brand_app_bar.dart';
import 'package:gomama/app/widgets/brand_buttons.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class SessionCleanNoticeView extends HookConsumerWidget {
  const SessionCleanNoticeView(
    this.sessionId,
    this.fromUnlockSessionView,
    this.fromUnlockSessionViewHasNotClickedLetsGo, {
    super.key,
  });
  final String sessionId;
  final bool fromUnlockSessionView;
  final bool fromUnlockSessionViewHasNotClickedLetsGo;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (fromUnlockSessionView) {
      return _Content(
        sessionId,
        220,
        fromUnlockSessionView,
        fromUnlockSessionViewHasNotClickedLetsGo,
      );
    }

    return Scaffold(
      backgroundColor: CustomColors.secondaryExtraLight,
      body: _Body(
        sessionId,
        fromUnlockSessionView,
        fromUnlockSessionViewHasNotClickedLetsGo,
      ),
    );
  }
}

class _Body extends HookConsumerWidget {
  const _Body(
    this.sessionId,
    this.fromUnlockSessionView,
    this.fromUnlockSessionViewHasNotClickedLetsGo,
  );
  double get maxExtent => 200 + kToolbarHeight;
  double get minExtent => kToolbarHeight;
  final String sessionId;
  final bool fromUnlockSessionView;
  final bool fromUnlockSessionViewHasNotClickedLetsGo;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return CustomScrollView(
      slivers: [
        SliverPersistentHeader(
          delegate: BrandAppBar(
            maxHeight:
                kToolbarHeight + 32 + mediaQuery(context).viewPadding.top,
            minHeight: kToolbarHeight + mediaQuery(context).viewPadding.top,
            title: const Text('Rate your experience, Mama!'),
            child: Row(
              children: [
                SizedBox(
                  height: max(
                    mediaQuery(context).viewPadding.top,
                    kToolbarHeight * 10,
                  ),
                ),
              ],
            ),
          ),
          pinned: true,
        ),
        SliverList.list(
          children: [
            _Content(
              sessionId,
              240,
              fromUnlockSessionView,
              fromUnlockSessionViewHasNotClickedLetsGo,
            ),
          ],
        ),
      ],
    );
  }
}

class _Content extends ConsumerWidget {
  const _Content(
    this.sessionId,
    this.logoSize,
    this.fromUnlockSessionView,
    this.fromUnlockSessionViewHasNotClickedLetsGo,
  );
  final String sessionId;
  final double logoSize;
  final bool fromUnlockSessionView;
  final bool fromUnlockSessionViewHasNotClickedLetsGo;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.all(32),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 32),
          Text(
            'Your session has been ended!',
            style: textTheme(context).headlineMedium,
          ),
          const SizedBox(height: 16),
          const Text(
            'Please rate the experience.',
          ),
          const SizedBox(height: 32),
          Image.asset(
            'assets/images/goma_whiteboard.png',
            height: logoSize,
          ),
          const SizedBox(height: 32),
          BrandButton.cta(
            onPressed: () {
              ref.invalidate(activeSessionProvider);

              if (fromUnlockSessionViewHasNotClickedLetsGo) {
                context.pop();
              } else {
                // if clicked means there is current_session_view behind, hence pop twice.
                context
                  ..pop()
                  ..pop();
              }

              showCupertinoModalPopup(
                barrierDismissible: false,
                context: context,
                builder: (context) => ReviewSessionView(
                  sessionId,
                ),
              );
            },
            child: const Text('Rate'),
          ),
        ],
      ),
    );
  }
}
