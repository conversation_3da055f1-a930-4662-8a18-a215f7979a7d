import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gomama/app/features/explore/model/nearby_listing_input.dart';
import 'package:gomama/app/features/explore/provider/explore_providers.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:gomama/app/features/listing/widget/filter_sheet.dart';
import 'package:gomama/app/features/listing/widget/listing_card.dart';
import 'package:gomama/app/features/maps/model/map_input.dart';
import 'package:gomama/app/features/maps/provider/position_providers.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'nearest_listing.g.dart';

@riverpod
int _nearestListingIndex(_NearestListingIndexRef ref) {
  throw UnimplementedError();
}

class NearestListing extends ConsumerWidget {
  const NearestListing({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref
      ..watch(amenityFiltersProvider)
      ..watch(listingSortsProvider);

    // TODO(kkcy): explore if possible to prevent rerender whole view on map moved
    final position = ref.watch(exploreMapPositionProvider);
    final devicePosition = ref.watch(currentPositionProvider).valueOrNull;
    final input = NearbyListingsInput(
      lat: position.latitude,
      lon: position.longitude,
      r: getSearchRadius(position.zoom),
      deviceLat: devicePosition?.latitude ?? position.latitude,
      deviceLon: devicePosition?.longitude ?? position.longitude,
    );
    final listingCount = ref.watch(nearbyListingsCountProvider(input));

    return listingCount.when(
      skipLoadingOnReload: true,
      skipLoadingOnRefresh: true,
      loading: () => const SliverToBoxAdapter(
        child: SizedBox.shrink(),
      ),
      error: (err, stack) => SliverToBoxAdapter(child: Text('Error $err')),
      data: (listingCount) {
        if (listingCount == 0) {
          // TODO(kkcy): prettier this
          return SliverToBoxAdapter(
            child: Center(
              child: Column(
                children: [
                  const SizedBox(height: 64),
                  Image.asset(
                    'assets/images/goma_sad.png',
                    height: 160,
                  ),
                  const Text('No listing found in this area.'),
                ],
              ),
            ),
          );
        }

        return SliverList.separated(
          itemBuilder: (context, index) {
            return ProviderScope(
              overrides: [
                // ignore: scoped_providers_should_specify_dependencies
                _nearestListingIndexProvider.overrideWithValue(index),
              ],
              child: const _ListingCard(),
            );
          },
          separatorBuilder: (context, index) {
            return const SizedBox(height: 16);
          },
          itemCount: listingCount,
        );
      },
    );
  }
}

class _ListingCard extends ConsumerWidget {
  const _ListingCard();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final index = ref.watch(_nearestListingIndexProvider);
    final position = ref.watch(exploreMapPositionProvider);
    final devicePosition = ref.watch(currentPositionProvider).requireValue!;
    final input = NearbyListingsInput(
      lat: position.latitude,
      lon: position.longitude,
      r: getSearchRadius(position.zoom),
      deviceLat: devicePosition.latitude,
      deviceLon: devicePosition.longitude,
    );
    final offset = NearbyListingsOffset(offset: index, input: input);
    final listing = ref.watch(
      listingAtIndexProvider(offset),
    );

    return listing.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (err, stack) => Text('Error $err'),
      data: (listing) {
        return ListingCard(listing);
      },
    );
  }
}
