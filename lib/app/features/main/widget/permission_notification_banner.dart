import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/local_storage/app_storage.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/features/main/model/permission_state.dart';
import 'package:gomama/app/features/main/provider/permission_providers.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class PermissionNotificationBanner extends ConsumerWidget {
  const PermissionNotificationBanner({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final permissionState = ref.watch(permissionsProvider).valueOrNull;

    // Don't show banner if permissions are loading or both permissions are granted
    if (permissionState == null ||
        (permissionState.hasLocationPermission &&
         permissionState.hasNotificationPermission)) {
      return const SizedBox.shrink();
    }

    return FutureBuilder<String?>(
      future: ref.read(securedAppStorageProvider).readValue('permission_banner_dismissed'),
      builder: (context, snapshot) {
        // Don't show if user has dismissed the banner
        if (snapshot.data == 'true') {
          return const SizedBox.shrink();
        }

        return _PermissionBannerContent(permissionState: permissionState);
      },
    );
  }
}

class _PermissionBannerContent extends ConsumerWidget {
  const _PermissionBannerContent({required this.permissionState});

  final PermissionState permissionState;

  @override
  Widget build(BuildContext context, WidgetRef ref) {

    // Determine what permissions are missing
    final needsLocation = !permissionState.hasLocationPermission;
    final needsNotification = !permissionState.hasNotificationPermission;
    
    String title;
    String subtitle;
    IconData icon;
    Color color;
    
    if (needsLocation && needsNotification) {
      title = 'Enable Location & Notifications';
      subtitle = 'Get personalized recommendations and important updates';
      icon = Icons.location_on;
      color = Colors.blue;
    } else if (needsLocation) {
      title = 'Enable Location Access';
      subtitle = 'Find nearby pods and get personalized recommendations';
      icon = Icons.location_on;
      color = Colors.blue;
    } else {
      title = 'Enable Notifications';
      subtitle = 'Stay updated with important reminders and tips';
      icon = Icons.notifications;
      color = Colors.orange;
    }

    return Container(
      margin: const EdgeInsets.fromLTRB(24, 8, 24, 4),
      child: Material(
        elevation: 2,
        borderRadius: BorderRadius.circular(12),
        child: DecoratedBox(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              colors: [
                color.withValues(alpha: 0.1),
                color.withValues(alpha: 0.05),
              ],
            ),
            border: Border.all(
              color: color.withValues(alpha: 0.3),
            ),
          ),
          child: InkWell(
            onTap: () {
              const PermissionRoute().push(context);
            },
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      icon,
                      color: color,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          title,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          subtitle,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // GestureDetector(
                      //   onTap: () async {
                      //     // Dismiss the banner
                      //     await ref.read(securedAppStorageProvider)
                      //         .writeValue('permission_banner_dismissed', 'true');
                      //     // Force rebuild to hide the banner
                      //     ref.invalidate(permissionsProvider);
                      //   },
                      //   child: Container(
                      //     padding: const EdgeInsets.all(4),
                      //     child: Icon(
                      //       Icons.close,
                      //       size: 16,
                      //       color: Colors.grey[600],
                      //     ),
                      //   ),
                      // ),
                      // const SizedBox(width: 8),
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 16,
                        color: Colors.grey[400],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
