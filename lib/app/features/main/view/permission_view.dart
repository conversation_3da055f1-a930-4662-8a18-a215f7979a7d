import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/features/main/provider/permission_providers.dart';
import 'package:gomama/app/features/maps/provider/position_providers.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionView extends HookConsumerWidget {
  const PermissionView({super.key});

  static const routeName = 'permission';
  static const routePath = '/permission';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    useOnAppLifecycleStateChange(
      (previous, current) {
        if (current == AppLifecycleState.resumed) {
          // ask for permissions again
          ref.invalidate(currentPositionProvider);
        }
      },
    );

    // Check if notification permission is also needed
    final permissionState = ref.watch(permissionsProvider).valueOrNull;
    final needsNotificationPermission =
        permissionState != null && !permissionState.hasNotificationPermission;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Enhance Your Experience'),
      ),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Center(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 24),
              child: Text(
                'Get the most out of GoMama',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          const SizedBox(height: 8),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 24),
            child: Text(
              'Enable location access to find nearby services and get personalized recommendations',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ),
          const SizedBox(height: 24),
          FilledButton(
            onPressed: () async {
              await openAppSettings();
            },
            child: const Text('Enable Location'),
          ),
          const SizedBox(height: 12),
          OutlinedButton(
            onPressed: () {
              // Navigate to the main app without permissions
              context.go('/explore');
            },
            child: const Text('Continue without location'),
          ),
          if (needsNotificationPermission) ...[
            const SizedBox(height: 48),
            const Divider(),
            const SizedBox(height: 16),
            const Text(
              'Enable notifications to receive important updates and reminders',
              style: TextStyle(fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            OutlinedButton(
              onPressed: () async {
                await openAppSettings();
              },
              child: const Text('Enable Notifications'),
            ),
            const SizedBox(height: 8),
            TextButton(
              onPressed: () {
                // Navigate to the main app without notifications
                context.go('/explore');
              },
              child: const Text('Skip notifications'),
            ),
          ],
        ],
      ),
    );
  }
}
