import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/features/main/provider/permission_providers.dart';
import 'package:gomama/app/features/maps/provider/position_providers.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionView extends HookConsumerWidget {
  const PermissionView({super.key});

  static const routeName = 'permission';
  static const routePath = '/permission';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Only refresh permission state when returning from Settings
    // Don't automatically re-request permissions to avoid spam
    useOnAppLifecycleStateChange(
      (previous, current) {
        if (current == AppLifecycleState.resumed) {
          // Only refresh the permission state, don't re-request
          ref.invalidate(permissionsProvider);
        }
      },
    );

    // Check if notification permission is also needed
    final permissionState = ref.watch(permissionsProvider).valueOrNull;
    final needsNotificationPermission =
        permissionState != null && !permissionState.hasNotificationPermission;

    // Track if we've already asked for permissions to avoid spam
    final hasAskedForLocationPermission = useState(false);
    final hasAskedForNotificationPermission = useState(false);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Location Access'),
      ),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.location_on_outlined,
            size: 64,
            color: Colors.blue,
          ),
          const SizedBox(height: 24),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 24),
            child: Text(
              'Enhance ',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
          ),
          const SizedBox(height: 16),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 24),
            child: Text(
              'To use location-based features like finding nearby pods and accessing location-specific content, GoMama needs access to your location.',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16),
            ),
          ),
          const SizedBox(height: 16),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 24),
            child: Text(
              'Features that require location access:',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
            ),
          ),
          const SizedBox(height: 8),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 40),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('• Find and access nearby Go!Mama pods'),
                Text('• Get distance-based recommendations'),
                Text('• Access location-specific content'),
              ],
            ),
          ),
          const SizedBox(height: 24),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: FilledButton(
              onPressed: () async {
                if (!hasAskedForLocationPermission.value) {
                  // First time - try to request permission directly
                  hasAskedForLocationPermission.value = true;

                  // Request permission directly first
                  final permission = await Permission.locationWhenInUse.request();

                  if (permission.isGranted) {
                    // Permission granted, refresh providers and navigate to main app
                    ref
                      ..invalidate(permissionsProvider)
                      ..invalidate(currentPositionProvider);
                    if (context.mounted) {
                      context.go('/explore');
                    }
                  }
                  // If not granted, user can still use the Settings button
                } else {
                  // User already denied once, take them to Settings
                  await openAppSettings();
                }
              },
              style: FilledButton.styleFrom(
                minimumSize: const Size(double.infinity, 48),
              ),
              child: Text(
                hasAskedForLocationPermission.value
                  ? 'Open Settings to Enable Location'
                  : 'Allow Location Access',
              ),
            ),
          ),
          const SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: OutlinedButton(
              onPressed: () {
                // Navigate to the main app without permissions
                context.go('/explore');
              },
              style: OutlinedButton.styleFrom(
                minimumSize: const Size(double.infinity, 48),
              ),
              child: const Text('Continue without location features'),
            ),
          ),
          const SizedBox(height: 16),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 24),
            child: Text(
              'You can enable location access later in Settings > Privacy & Security > Location Services',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ),
          if (needsNotificationPermission) ...[
            const SizedBox(height: 32),
            const Divider(),
            const SizedBox(height: 24),
            const Icon(
              Icons.notifications_outlined,
              size: 48,
              color: Colors.orange,
            ),
            const SizedBox(height: 16),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 24),
              child: Text(
                'Stay Updated with Notifications',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
            const SizedBox(height: 8),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 24),
              child: Text(
                'Enable notifications to receive important updates, appointment reminders, and health tips.',
                style: TextStyle(fontSize: 14),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: OutlinedButton(
                onPressed: () async {
                  if (!hasAskedForNotificationPermission.value) {
                    // First time - try to request permission directly
                    hasAskedForNotificationPermission.value = true;

                    // Request notification permission directly first
                    final permission = await Permission.notification.request();

                    if (permission.isGranted) {
                      // Permission granted, refresh providers and navigate to main app
                      ref.invalidate(permissionsProvider);
                      if (context.mounted) {
                        context.go('/explore');
                      }
                    }
                    // If not granted, user can still use the Settings button
                  } else {
                    // User already denied once, take them to Settings
                    await openAppSettings();
                  }
                },
                style: OutlinedButton.styleFrom(
                  minimumSize: const Size(double.infinity, 48),
                ),
                child: Text(
                  hasAskedForNotificationPermission.value
                    ? 'Enable Notifications in Settings'
                    : 'Allow Notifications',
                ),
              ),
            ),
            const SizedBox(height: 8),
            TextButton(
              onPressed: () {
                // Navigate to the main app without notifications
                context.go('/explore');
              },
              child: const Text('Skip for now'),
            ),
          ],
        ],
      ),
    );
  }
}
