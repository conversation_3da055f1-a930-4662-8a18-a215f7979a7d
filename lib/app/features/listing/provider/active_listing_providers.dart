import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/network/mqtt_models.dart';
import 'package:gomama/app/core/network/mqtt_service.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:gomama/app/features/listing/provider/active_listing_status_providers.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'active_listing_providers.g.dart';

/// NOTE: backend notifies this listing's status in realtime via SSE
@riverpod
class ActiveListing extends _$ActiveListing {
  @protected
  MqttService get mqttService => ref.read(mqttServiceProvider);

  @override
  ListingStatus? build(String listingId) {
    mqttService.subscribeToListingStatus(listingId);

    // Use typed stream for better type safety
    mqttService.listingStatusStream.listen(
      (update) {
        // Check if message is for this specific listing
        if (update.listingId == listingId) {
          _handleStatusUpdate(update);
        }
      },
    );

    // Keep backward compatibility with generic messages for other message types
    mqttService.messages.listen(
      (message) {
        final topic = message['topic'] as String?;
        if (topic == 'gomama/listings/status/$listingId') {
          _handleStatusEvent(message);
        }
      },
    );

    return null;
  }

  void _handleStatusUpdate(ListingStatusUpdate update) {
    Groveman.debug('active listing ${update.listingId} status: ${update.status}');

    switch (update.status) {
      case 'idle':
        state = ListingStatus.idle;
      case 'occupied':
        state = ListingStatus.occupied;
      case 'disinfecting':
        state = ListingStatus.disinfecting;
    }

    // set activelistingstatus
    if (state != null) {
      ref
          .read(activeListingsStatusProvider.notifier)
          .upsertStatus(listingId, state!);
    }
  }

  void _handleStatusEvent(Json data) {
    Groveman.debug('active listing $listingId', error: data);

    switch (data['data']) {
      case 'idle':
        state = ListingStatus.idle;
      case 'occupied':
        state = ListingStatus.occupied;
      case 'disinfecting':
        state = ListingStatus.disinfecting;
    }

    // set activelistingstatus
    if (state != null) {
      ref
          .read(activeListingsStatusProvider.notifier)
          .upsertStatus(listingId, state!);
    }
  }
}
