import 'dart:io';
import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_branch_sdk/flutter_branch_sdk.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/constants/environment.dart';
import 'package:gomama/app/core/router/routes.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/favourites/provider/favourite_providers.dart';
import 'package:gomama/app/features/favourites/provider/user_favourite_providers.dart';
import 'package:gomama/app/features/listing/model/amenities.dart';
import 'package:gomama/app/features/listing/model/listing_ratings.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:gomama/app/features/listing/model/update_favourite_listings_input.dart';
import 'package:gomama/app/features/listing/provider/active_listing_providers.dart';
import 'package:gomama/app/features/listing/provider/listing_providers.dart';
import 'package:gomama/app/features/listing/provider/session_duration_providers.dart';
import 'package:gomama/app/features/listing/view/review_listing_view.dart';
import 'package:gomama/app/features/listing/widget/flag_listing_expansion_tile.dart';
import 'package:gomama/app/features/listing/widget/help_support_expansion_tile.dart';
import 'package:gomama/app/features/listing/widget/listing_detail_expansion_tile.dart';
import 'package:gomama/app/features/listing/widget/listing_gallery.dart';
import 'package:gomama/app/features/main/provider/permission_providers.dart';
import 'package:gomama/app/features/session/provider/session_providers.dart';
import 'package:gomama/app/features/session/view/unlock_session_view.dart';
import 'package:gomama/app/features/verification/provider/verification_providers.dart';
import 'package:gomama/app/widgets/brand_buttons.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class ListingView extends HookConsumerWidget {
  const ListingView({
    this.initialListing,
    required this.listingId,
    super.key,
  });

  final String listingId;
  final Listing? initialListing;

  static const routeName = 'listing';
  static const routePath = '/listings/:listingId';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sessionEventAsync = ref.watch(activeSessionProvider);
    final listingAsync = ref.watch(
      singleListingProvider(initialListing?.id ?? listingId),
    );

    // final _scrollController = useScrollController();
    // final percentage = useState<double>(0);
    // const maxScrollExtent = 200;

    // void _onChanged() {
    //   final currentScrollOffset = _scrollController.offset;
    //   // offset.value = (currentScrollOffset / maxScrollExtent) * 100 * speed;
    //   percentage.value = currentScrollOffset / maxScrollExtent;
    // }

    // useEffect(
    //   () {
    //     _scrollController.addListener(_onChanged);

    //     return () => _scrollController.dispose;
    //   },
    //   [],
    // );

    // TODO(kkcy): do we still need to force reset this now that we are using sockets
    useOnAppLifecycleStateChange(
      (previous, current) {
        if (current == AppLifecycleState.resumed) {
          /// NOTE: invalidate everytime
          // TODO(kkcy): can we invalidate without passing listing.id
          ref.invalidate(activeListingProvider);
        }
      },
    );

    return Scaffold(
      backgroundColor: CustomColors.secondaryExtraLight,
      body: listingAsync.when(
        error: (error, stackTrace) {
          Groveman.error('ListingView', error: error, stackTrace: stackTrace);
          return const SizedBox.shrink();
        },
        loading: () {
          if (initialListing == null) {
            // only if we entered via deeplink
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          // while loading, we show what we have
          return _Body(initialListing!);
        },
        data: (listing) {
          // on loaded, we show updated information
          return _Body(listing);
        },
      ),
      // bottomNavigationBar: activeSession.requireValue == null
      //     ? initialListing.listingType == ListingType.gomama
      //         ? _Footer(initialListing.id)
      //         : null
      //     : null,
      bottomNavigationBar: sessionEventAsync.when(
        error: (error, stackTrace) {
          Groveman.error(
            'ListingView - activeSession',
            error: error,
            stackTrace: stackTrace,
          );

          // handle activeSessionError that we support
          // if (handledActiveSessionError(
          //   error as Exception,
          //   withDefault: false,
          // )) {
          //   // let user try again
          //   if (initialListing?.listingType == ListingType.gomama) {
          //     return _Footer(initialListing!);
          //   }
          // }

          // Always show the unlock footer for gomama listings, even after unhandled errors
          if (initialListing?.listingType == ListingType.gomama) {
            return _Footer(initialListing!);
          }

          return null;
        },
        loading: () {
          return null;
        },
        data: (sessionEvent) {
          return sessionEvent?.session == null
              ? initialListing?.listingType == ListingType.gomama
                  ? _Footer(initialListing!)
                  : null
              : null;
        },
      ),
    );
  }
}

class _Body extends ConsumerWidget {
  const _Body(this.listing);
  final Listing listing;

  double get maxExtent => 200 + kToolbarHeight;
  double get minExtent => kToolbarHeight;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isFavourite =
        ref.watch(userFavouriteListingsProvider).contains(listing.id);

    return NestedScrollView(
      // controller: _scrollController,
      headerSliverBuilder: (context, innerBoxIsScrolled) => [
        SliverOverlapAbsorber(
          handle: NestedScrollView.sliverOverlapAbsorberHandleFor(
            context,
          ),
          sliver: SliverSafeArea(
            top: false,
            sliver: SliverAppBar(
              backgroundColor: CustomColors.secondaryLight,
              // foregroundColor: Colors.white,
              // actionsIconTheme:
              //     const IconThemeData(color: Colors.white),
              // scrolledUnderElevation: 0,
              leading: Center(
                child: BackButton(
                  style: ButtonStyle(
                    backgroundColor: WidgetStateProperty.all(Colors.white),
                    iconColor: WidgetStateProperty.all(CustomColors.primary),
                  ),
                  onPressed: () {
                    if (context.canPop()) {
                      context.pop();
                    } else {
                      // If we can't pop, we came from a deep link
                      const ExploreRoute().go(context);
                    }
                  },
                ),
              ),
              actions: [
                IconButton(
                  onPressed: () async {
                    final address = listing.fullAddress ?? '';
                    final encodedAddress = Uri.encodeComponent(address);
                    String url;
                    if (Platform.isIOS) {
                      // Apple Maps URL scheme
                      url = 'https://maps.apple.com/?q=$encodedAddress';
                    } else {
                      // Google Maps URL for other platforms
                      url =
                          'https://www.google.com/maps/search/?api=1&query=$encodedAddress';
                    }

                    if (await canLaunchUrl(Uri.parse(url))) {
                      await launchUrl(Uri.parse(url));
                    } else {
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                              'Could not launch the URL, please try again',
                            ),
                          ),
                        );
                      }
                    }
                  },
                  icon: const Icon(CustomIcon.location),
                  style: ButtonStyle(
                    backgroundColor: WidgetStateProperty.all(Colors.white),
                    iconColor: WidgetStateProperty.all(CustomColors.primary),
                  ),
                ),
                IconButton(
                  onPressed: () async {
                    try {
                      final buo = BranchUniversalObject(
                        canonicalIdentifier: '/listings/${listing.id}',
                        title: listing.name ?? '',
                        contentMetadata: BranchContentMetaData()
                          ..addCustomMetadata('listingId', listing.id)
                          ..addCustomMetadata('listingName', listing.name),
                      );

                      FlutterBranchSdk.registerView(
                        buo: buo,
                      );

                      final lp = BranchLinkProperties(
                        feature: 'sharing',
                        channel: 'app',
                      );

                      final response = await FlutterBranchSdk.getShortUrl(
                        buo: buo,
                        linkProperties: lp,
                      );

                      await Share.share(response.result as String);
                    } catch (e) {
                      debugPrint(e.toString());
                    }

                    // final result = await Share.share(
                    //   'Share this listing',
                    //   subject: 'Share Gomama to create friendly community!',
                    // );

                    // switch (result.status) {
                    //   // TODO(ch): Change print to snackbars
                    //   case ShareResultStatus.success:
                    //     Groveman.debug('Thank you for sharing!');
                    //     break;
                    //   case ShareResultStatus.dismissed:
                    //     Groveman.debug('Share cancelled');
                    //     break;
                    //   case ShareResultStatus.unavailable:
                    //     Groveman.debug(
                    //       'Sharing is not available on this device',
                    //     );
                    //     break;
                    // }
                  },
                  icon: const Icon(CustomIcon.share),
                  style: ButtonStyle(
                    backgroundColor: WidgetStateProperty.all(Colors.white),
                    iconColor: WidgetStateProperty.all(CustomColors.primary),
                  ),
                ),
                IconButton(
                  onPressed: () async {
                    final input = UpdateFavouriteListingsInput(
                      listingId: listing.id,
                    );

                    final success = await ref.read(
                      updateFavouriteListingsProvider(input).future,
                    );

                    if (success && context.mounted) {
                      if (isFavourite) {
                        ref
                            .read(userFavouriteListingsProvider.notifier)
                            .remove(listing.id);
                      } else {
                        ref
                            .read(userFavouriteListingsProvider.notifier)
                            .add(listing.id);
                      }

                      //   ScaffoldMessenger.of(context).showSnackBar(
                      //     const SnackBar(
                      //       content: Text('Favorites updated'),
                      //     ),
                      //   );
                    }
                  },
                  icon: Icon(
                    isFavourite ? CustomIcon.heart : CustomIcon.heartEmpty,
                  ),
                  style: ButtonStyle(
                    backgroundColor: WidgetStateProperty.all(Colors.white),
                    iconColor: WidgetStateProperty.all(CustomColors.primary),
                  ),
                ),
              ],
              expandedHeight: maxExtent,
              // collapsedHeight: kToolbarHeight,
              pinned: true,
              // flexibleSpace: LayoutBuilder(
              //   builder: (context, constraints) {
              //     // final currentExtent = constraints.maxHeight;
              //     // final deltaExtent = maxExtent - minExtent;
              //     // 0.0 -> Expanded
              //     // 1.0 -> Collapsed to toolbar
              //     // final t = (1.0 -
              //     //         (currentExtent - minExtent) / deltaExtent)
              //     //     .clamp(0.0, 1.0);

              //     return Stack(
              //       children: [
              //         Positioned(
              //           top: 0, //_getCollapsePadding(t),
              //           left: 0,
              //           right: 0,
              //           height: maxExtent,
              //           child: _ListingHero(listing),
              //         ),
              //       ],
              //     );
              //   },
              // ),
              flexibleSpace: FlexibleSpaceBar(
                collapseMode: CollapseMode.none,
                background: ListingGallery(
                  listing,
                  radius: BorderRadius.zero,
                  isUnderGalleryView: true,
                ),
              ),
            ),
          ),
        ),
      ],
      body: SingleChildScrollView(
        child: _ListingContent(
          listing,
          // percentage.value,
        ),
      ),
    );

    // return Stack(
    //   children: [
    //     NestedScrollView(
    //       // controller: _scrollController,
    //       headerSliverBuilder: (context, innerBoxIsScrolled) => [
    //         SliverOverlapAbsorber(
    //           handle: NestedScrollView.sliverOverlapAbsorberHandleFor(
    //             context,
    //           ),
    //           sliver: SliverSafeArea(
    //             top: false,
    //             sliver: SliverAppBar(
    //               backgroundColor: Colors.transparent,
    //               // foregroundColor: Colors.white,
    //               // actionsIconTheme:
    //               //     const IconThemeData(color: Colors.white),
    //               // scrolledUnderElevation: 0,
    //               actions: [
    //                 IconButton(
    //                   onPressed: () {
    //                     // TODO(kkcy): add to user favourites
    //                   },
    //                   icon: const Icon(
    //                     Icons.favorite_outline,
    //                   ),
    //                 ),
    //               ],
    //               expandedHeight: maxExtent,
    //               // collapsedHeight: kToolbarHeight,
    //               pinned: true,
    //               // flexibleSpace: LayoutBuilder(
    //               //   builder: (context, constraints) {
    //               //     // final currentExtent = constraints.maxHeight;
    //               //     // final deltaExtent = maxExtent - minExtent;
    //               //     // 0.0 -> Expanded
    //               //     // 1.0 -> Collapsed to toolbar
    //               //     // final t = (1.0 -
    //               //     //         (currentExtent - minExtent) / deltaExtent)
    //               //     //     .clamp(0.0, 1.0);

    //               //     return Stack(
    //               //       children: [
    //               //         Positioned(
    //               //           top: 0, //_getCollapsePadding(t),
    //               //           left: 0,
    //               //           right: 0,
    //               //           height: maxExtent,
    //               //           child: _ListingHero(listing),
    //               //         ),
    //               //       ],
    //               //     );
    //               //   },
    //               // ),
    //               flexibleSpace: FlexibleSpaceBar(
    //                 collapseMode: CollapseMode.none,
    //                 background: ListingGallery(
    //                   listing,
    //                   radius: BorderRadius.zero,
    //                 ),
    //               ),
    //             ),
    //           ),
    //         ),
    //       ],
    //       body: SingleChildScrollView(
    //         child: _ListingContent(
    //           listing,
    //           // percentage.value,
    //         ),
    //       ),
    //     ),
    //     // Positioned.fill(
    //     //   child: Transform.translate(
    //     //     offset: Offset(
    //     //       0,
    //     //       clampDouble(-180 + (percentage.value * 180), -180, 0),
    //     //     ),
    //     //     child: const IgnorePointer(
    //     //       child: DecoratedBox(
    //     //         decoration: BoxDecoration(
    //     //           image: DecorationImage(
    //     //             image: AssetImage(
    //     //               'assets/backgrounds/milk_background_appbar.png',
    //     //             ),
    //     //             fit: BoxFit.fitWidth,
    //     //             alignment: Alignment.topCenter,
    //     //           ),
    //     //         ),
    //     //       ),
    //     //     ),
    //     //   ),
    //     // ),
    //     // Positioned(
    //     //   top: kTextTabBarHeight,
    //     //   left: 8,
    //     //   child: IconButton.filled(
    //     //     onPressed: () {
    //     //       context.pop();
    //     //     },
    //     //     icon: const Icon(Icons.arrow_back),
    //     //   ),
    //     // ),
    //   ],
    // );
  }
}

class _Footer extends ConsumerWidget {
  const _Footer(this.listing);
  final Listing listing;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    /// user should definitely be valid
    /// will be redirected if not signed in
    final user = ref.watch(authControllerProvider).requireValue;
    final duration = ref.watch(sessionDurationProvider);

    // NOTE: listens to the real time status of this listing
    final realtimeListingStatus = listing.listingType == ListingType.gomama
        ? ref.watch(activeListingProvider(listing.firestoreId!))
        : null;

    if (!user.isVerified) {
      return DecoratedBox(
        decoration: const BoxDecoration(
          color: CustomColors.secondaryLight,
          border: Border(
            top: BorderSide(color: CustomColors.secondary),
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: EdgeInsets.fromLTRB(
              16,
              20,
              16,
              mediaQuery(context).padding.bottom > 0
                  ? 0
                  : 20, // with notch ? 0 : 20
            ),
            child: SizedBox(
              height: 50,
              child: BrandButton.cta(
                onPressed: () {
                  const VerificationRoute().push(context);
                },
                child: const Text('Request for Access'),
              ),
            ),
          ),
        ),
      );
    }

    // admin verified ignore gender and child
    if (!(user.isAdminVerified ?? false)) {
      if (user.gender?.toLowerCase() != 'female') {
        return DecoratedBox(
          decoration: const BoxDecoration(
            color: CustomColors.secondaryLight,
            border: Border(
              top: BorderSide(color: CustomColors.secondary),
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: EdgeInsets.fromLTRB(
                16,
                20,
                16,
                mediaQuery(context).padding.bottom > 0
                    ? 0
                    : 20, // with notch ? 0 : 20
              ),
              child: Text(
                'Thank you for your interest. These pods are exclusively for breastfeeding mothers with babies under 36 months. We appreciate your understanding.',
                style: textTheme(context)
                    .bodyLarge!
                    .copyWith(color: CustomColors.primary),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        );
      }

      if (!user.hasValidChild) {
        return DecoratedBox(
          decoration: const BoxDecoration(
            color: CustomColors.secondaryLight,
            border: Border(
              top: BorderSide(color: CustomColors.secondary),
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: EdgeInsets.fromLTRB(
                16,
                20,
                16,
                mediaQuery(context).padding.bottom > 0
                    ? 0
                    : 20, // with notch ? 0 : 20
              ),
              child: SizedBox(
                height: 50,
                child: BrandButton.cta(
                  onPressed: () {
                    ref.read(
                      sendWhatsAppMessageProvider(
                        'Hi GO!MAMA, I need some help on listing access',
                        Environment.gomamaWhatsappPhone,
                      ),
                    );
                  },
                  child: const Text(
                    'Request for Access - Contact Support for Access\n',
                    style: TextStyle(fontSize: 12.8),
                  ),
                ),
              ),
            ),
          ),
        );
      }
    }

    final listingStatus = realtimeListingStatus ?? listing.status;

    if (listingStatus == ListingStatus.disinfecting) {
      return DecoratedBox(
        decoration: const BoxDecoration(
          color: CustomColors.secondaryLight,
          border: Border(
            top: BorderSide(color: CustomColors.secondary),
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: EdgeInsets.fromLTRB(
              16,
              20,
              16,
              mediaQuery(context).padding.bottom > 0
                  ? 0
                  : 20, // with notch ? 0 : 20
            ),
            child: Text(
              'Listing undergoing disinfection, please try again later.\n',
              style: textTheme(context)
                  .bodyLarge!
                  .copyWith(color: CustomColors.primary),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      );
    }

    if (listingStatus == ListingStatus.occupied) {
      return DecoratedBox(
        decoration: const BoxDecoration(
          color: CustomColors.secondaryLight,
          border: Border(
            top: BorderSide(color: CustomColors.secondary),
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: EdgeInsets.fromLTRB(
              16,
              20,
              16,
              mediaQuery(context).padding.bottom > 0
                  ? 0
                  : 20, // with notch ? 0 : 20
            ),
            child: Text(
              'Listing is currently occupied,\nplease try again later.',
              style: textTheme(context)
                  .bodyLarge!
                  .copyWith(color: CustomColors.primary),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      );
    }

    return DecoratedBox(
      decoration: const BoxDecoration(
        color: CustomColors.secondaryLight,
        border: Border(
          top: BorderSide(color: CustomColors.secondary),
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.fromLTRB(
            16,
            20,
            16,
            mediaQuery(context).padding.bottom > 0
                ? 0
                : 20, // with notch ? 0 : 20
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const SizedBox(
                width: 160,
                child: _NumberInputWithIncrementDecrement(),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: SizedBox(
                  height: 50,
                  child: Consumer(
                    builder: (context, ref, child) {
                      final permissionState = ref.watch(permissionsProvider).valueOrNull;
                      final hasLocationPermission = permissionState?.hasLocationPermission ?? false;

                      return BrandButton.cta(
                        onPressed: () {
                          if (!hasLocationPermission) {
                            // Show location permission required dialog
                            showDialog(
                              context: context,
                              builder: (context) => AlertDialog(
                                title: const Text('Location Access Required'),
                                content: const Text(
                                  'To unlock a pod and create a session, GoMama needs access to your location to verify you are near the pod.',
                                ),
                                actions: [
                                  TextButton(
                                    onPressed: () => Navigator.of(context).pop(),
                                    child: const Text('Cancel'),
                                  ),
                                  FilledButton(
                                    onPressed: () {
                                      Navigator.of(context).pop();
                                      const PermissionRoute().push(context);
                                    },
                                    child: const Text('Enable Location'),
                                  ),
                                ],
                              ),
                            );
                            return;
                          }

                          // check if user is allow to unlock listing
                          showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            builder: (context) => UnlockListingViewSheet(
                              listingId: listing.id,
                              duration: duration,
                            ),
                          );
                        },
                        child: Text(
                          hasLocationPermission ? 'Unlock' : 'Enable Location to Unlock',
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _ListingContent extends ConsumerWidget {
  const _ListingContent(
    this.listing,
    // this.offsetPercentage
  );
  final Listing listing;
  // final double offsetPercentage;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        // SizedBox(height: clampDouble(offsetPercentage * 240, 16, 240)),
        // Padding(
        //   padding: const EdgeInsets.symmetric(horizontal: 16),
        //   child: Row(
        //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
        //     children: [
        //       if (listing.distance != null)
        //         Text('${listing.distance!.toStringAsFixed(0)}m away')
        //       else
        //         const SizedBox.shrink(),
        //     ],
        //   ),
        // ),
        // const Divider(),
        // Transform.translate(
        //   offset: Offset(
        //     0,
        //     // gradually speed up as it reach maxExtend
        //     -pow(offsetPercentage * 80, 1.15).toDouble(),
        //     // -(offsetPercentage * offsetPercentage) * 100,
        //   ),
        //   child: Column(
        //     children: [
        //       _ListingOverview(listing),
        //       const SizedBox(height: 24),
        //       const Padding(
        //         padding: EdgeInsets.symmetric(horizontal: 16),
        //         child: Divider(),
        //       ),
        //       _ListingAmenities(listing),
        //       const Padding(
        //         padding: EdgeInsets.symmetric(horizontal: 16),
        //         child: Divider(),
        //       ),
        //       _ListingReviews(listing),
        //       const Padding(
        //         padding: EdgeInsets.symmetric(horizontal: 16),
        //         child: Divider(),
        //       ),
        //       ListingDetail(listing),
        //       const Padding(
        //         padding: EdgeInsets.symmetric(horizontal: 16),
        //         child: Divider(),
        //       ),
        //       HelpSupport(listing.openingHours ?? ''),
        //       const Padding(
        //         padding: EdgeInsets.symmetric(horizontal: 16),
        //         child: Divider(),
        //       ),
        //       Row(
        //         mainAxisAlignment: MainAxisAlignment.center,
        //         children: [
        //           const Icon(
        //             Icons.flag,
        //             color: Colors.red,
        //           ),
        //           TextButton(
        //             onPressed: () {
        //               const FlagListingRoute().push(context);
        //             },
        //             style: TextButton.styleFrom(
        //               foregroundColor: Colors.red,
        //             ),
        //             child: const Text(
        //               'Flag this listing',
        //               style: TextStyle(
        //                 decoration: TextDecoration.underline,
        //                 decorationColor: Colors.red,
        //               ),
        //             ),
        //           ),
        //         ],
        //       ),
        //     ],
        //   ),
        // ),
        SizedBox(
          height: mediaQuery(context).padding.bottom > 0 ? 0 : 20,
        ),
        _ListingOverview(listing),
        const SizedBox(height: 24),
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16),
          child: Divider(),
        ),
        _ListingAmenities(listing),
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16),
          child: Divider(),
        ),
        _ListingReviews(listing),
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16),
          child: Divider(),
        ),
        ListingDetail(listing),
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16),
          child: Divider(),
        ),
        HelpSupport(listing.openingHours ?? ''),
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16),
          child: Divider(),
        ),
        FlagListing(listing),
        const SizedBox(height: 24),
      ],
    );
  }
}

class _ListingOverview extends ConsumerWidget {
  const _ListingOverview(this.listing);
  final Listing listing;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  listing.name ?? '',
                  style: textTheme(context).titleLarge!.copyWith(
                        color: CustomColors.primary,
                        fontFamily: 'AveriaSansLibre',
                      ),
                ),
              ),
              if (listing.distance != null)
                Text(
                  '${listing.distance!.toStringAsFixed(0)}m',
                  style: textTheme(context)
                      .labelMedium!
                      .copyWith(color: CustomColors.primary),
                )
              else
                const SizedBox.shrink(),
            ],
          ),
          // if company exist
          if (listing.companyName != null)
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                'by ${listing.companyName}',
                style: textTheme(context)
                    .labelMedium!
                    .copyWith(color: CustomColors.primary),
              ),
            ),
          const SizedBox(height: 24),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Padding(
                // to accomodate if full address is 2 lines
                padding: EdgeInsets.only(top: 3),
                child: Icon(
                  CustomIcon.location,
                  size: 16,
                ),
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(listing.fullAddress ?? ''),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              const Icon(CustomIcon.time, size: 16),
              const SizedBox(width: 4),
              Expanded(child: Text(listing.openingHours ?? '')),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              const Icon(CustomIcon.star, size: 16),
              const SizedBox(width: 4),
              Text(
                listing.averageExperienceRatings?.toStringAsFixed(2) ?? '',
              ),
              const SizedBox(width: 8),
              Text(
                '• ${listing.totalExperienceRatings} reviews',
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _ListingAmenities extends HookConsumerWidget {
  const _ListingAmenities(this.listing);
  final Listing listing;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 8),
          Text('Amenities', style: textTheme(context).titleMedium),
          const SizedBox(height: 20),
          // GridView.builder(
          //   padding: EdgeInsets.zero,
          //   shrinkWrap: true,
          //   physics: const NeverScrollableScrollPhysics(),
          //   gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          //     crossAxisCount: 2,
          //     childAspectRatio: 155 / 45,
          //     crossAxisSpacing: 10,
          //     mainAxisSpacing: 10,
          //   ),
          //   itemBuilder: (context, index) {
          //     return Material(
          //       color: CustomColors.secondaryLight,
          //       shape: RoundedRectangleBorder(
          //         borderRadius: BorderRadius.circular(8),
          //       ),
          //       child: Padding(
          //         padding: const EdgeInsets.symmetric(
          //           horizontal: 16,
          //           vertical: 8,
          //         ),
          //         child: Row(
          //           children: [
          //             const CircleAvatar(radius: 12),
          //             const SizedBox(width: 12),
          //             Expanded(
          //               child: Text(
          //                 listing.amenities![index].name,
          //                 style: textTheme(context)
          //                     .labelSmall!
          //                     .copyWith(height: 1.2),
          //               ),
          //             ),
          //           ],
          //         ),
          //       ),
          //     );
          //   },
          //   itemCount: listing.amenities?.length ?? 0,
          // ),
          GridView.count(
            shrinkWrap: true,
            childAspectRatio: 30 / 12,
            crossAxisCount: 2,
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.zero,
            children: List.generate(
              listing.amenities?.isNotEmpty == true
                  ? min(listing.amenities!.length, 4)
                  : 0,
              (index) {
                return Card(
                  child: Row(
                    children: [
                      const SizedBox(width: 16),
                      Icon(
                        amenityIconMap[listing.amenities![index].fontIconName],
                        color: CustomColors.primary,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          listing.amenities![index].name,
                          style: textTheme(context).labelMedium!.copyWith(
                                height: 1.2,
                                color: CustomColors.primary,
                              ),
                        ),
                      ),
                      const SizedBox(width: 16),
                    ],
                  ),
                );

                // return FilterChip(
                //   avatar: CircleAvatar(
                //     child: CachedNetworkImage(
                //       imageUrl: amenities[index].imageUrl,
                //       errorWidget: (context, url, error) =>
                //           const Icon(Icons.error, size: 10),
                //     ),
                //   ),
                //   label: Text(amenities[index].name),
                //   shape: RoundedRectangleBorder(
                //     borderRadius: BorderRadius.circular(16),
                //     side: const BorderSide(
                //       color: Colors.transparent,
                //     ),
                //   ),
                //   visualDensity: VisualDensity.compact,
                //   selected: filtering.value
                //       .contains(amenities[index].id),
                //   backgroundColor: Colors.grey.shade200,
                //   selectedColor: CustomColors.primary,
                //   labelStyle: Theme.of(context)
                //       .textTheme
                //       .labelLarge!
                //       .copyWith(
                //         color: filtering.value
                //                 .contains(amenities[index].id)
                //             ? Colors.white
                //             : Colors.black,
                //       ),
                //   onSelected: (value) {
                //     // ref
                //     //     .read(amenityFiltersProvider.notifier)
                //     //     .toggleAmenity(amenities[index].id);
                //     if (filtering.value
                //         .contains(amenities[index].id)) {
                //       filtering.value
                //           .remove(amenities[index].id);
                //     } else {
                //       filtering.value
                //           .add(amenities[index].id);
                //     }

                //     filtering.value = [...filtering.value];
                //   },
                // );
              },
              growable: false,
            ),
          ),
          const SizedBox(height: 8),
          BrandButton.outlined(
            onPressed: () {
              ListingAmenitiesRoute(listing.id, listing).push(context);
            },
            child: const Text(
              'Show all amenities',
            ),
          ),
        ],
      ),
    );
  }
}

class _ListingReviews extends HookConsumerWidget {
  const _ListingReviews(this.listing);
  final Listing listing;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 8),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Reviews', style: textTheme(context).titleMedium),
              Row(
                children: [
                  const Icon(CustomIcon.star, size: 16),
                  const SizedBox(width: 4),
                  Text(
                    listing.averageExperienceRatings?.toStringAsFixed(2) ?? '',
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 155,
          child: ListView.separated(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            scrollDirection: Axis.horizontal,
            itemBuilder: (context, index) {
              if (index == (listing.listingRatings?.length ?? 0) &&
                  listing.listingType == ListingType.care) {
                return Card(
                  child: InkWell(
                    onTap: () {
                      showCupertinoModalPopup(
                        barrierDismissible: false,
                        context: context,
                        builder: (context) => ReviewListingView(
                          listing.id,
                          listing: listing,
                        ),
                      );
                    },
                    borderRadius: const BorderRadius.all(Radius.circular(16)),
                    child: const AspectRatio(
                      aspectRatio: 190 / 255,
                      child: Icon(CustomIcon.plus),
                    ),
                  ),
                );
              }
              return _ReviewCard(listing.listingRatings![index]);
            },
            separatorBuilder: (context, index) {
              return const SizedBox(width: 16);
            },
            itemCount: listing.listingType == ListingType.care
                ? (listing.listingRatings?.length ?? 0) + 1
                : (listing.listingRatings?.length ?? 0),
          ),
        ),
        const SizedBox(height: 16),
        if (listing.totalExperienceRatings == 0)
          const SizedBox.shrink()
        else
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: BrandButton.outlined(
              onPressed: () {
                ListingReviewsRoute(listing.id, listing).push(context);
              },
              child: Text(
                'Show all ${listing.totalExperienceRatings ?? ''} reviews',
              ),
            ),
          ),
        const SizedBox(height: 8),
      ],
    );
  }
}

class _ReviewCard extends ConsumerWidget {
  const _ReviewCard(this.rating);
  final ListingRating rating;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      child: AspectRatio(
        aspectRatio: 255 / 190,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      for (int i = 0;
                          i < (rating.experienceRating ?? 0).ceil();
                          i++)
                        const Icon(
                          CustomIcon.star,
                          color: CustomColors.primary,
                          size: 10,
                        ),
                      const SizedBox(width: 2),
                      Text(
                        rating.experienceRating?.toStringAsFixed(1) ?? '5.0',
                        style: textTheme(context)
                            .labelSmall!
                            .copyWith(color: CustomColors.primary),
                      ),
                    ],
                  ),
                  if (rating.createdAt != null)
                    Text(
                      DateFormat.yMMMd().format(rating.createdAt!),
                      style: textTheme(context)
                          .labelSmall!
                          .copyWith(color: CustomColors.primary),
                    ),
                ],
              ),
              const SizedBox(height: 12),
              Expanded(child: Text(rating.review ?? 'abcd')),
              if ((rating.session?.user?.username ?? '').isEmpty)
                Text(
                  'anonymous',
                  style: textTheme(context)
                      .labelSmall!
                      .copyWith(color: CustomColors.primary),
                )
              else
                Text(
                  '@${rating.session?.user?.username}',
                  style: textTheme(context)
                      .labelSmall!
                      .copyWith(color: CustomColors.primary),
                ),
              const SizedBox(height: 12),
            ],
          ),
        ),
      ),
    );
  }
}

class _NumberInputWithIncrementDecrement extends HookConsumerWidget {
  const _NumberInputWithIncrementDecrement();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentDuration = ref.watch(sessionDurationProvider);
    return DecoratedBox(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(8)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          IconButton(
            icon: const Icon(CustomIcon.minusCircle),
            onPressed: () {
              if (currentDuration > 30) {
                ref.watch(sessionDurationProvider.notifier).decrement();
              } else {
                return;
              }
            },
          ),
          Expanded(
            child: Text(
              '$currentDuration',
              textAlign: TextAlign.center,
              // style: const TextStyle(
              //   fontSize: 20,
              // ),
            ),
          ),
          IconButton(
            icon: const Icon(CustomIcon.plusCircled),
            onPressed: () {
              if (currentDuration >= 45) {
                return;
              } else {
                ref.watch(sessionDurationProvider.notifier).increment();
              }
            },
          ),
        ],
      ),
    );
  }
}
