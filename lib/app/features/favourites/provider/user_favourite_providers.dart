import 'package:dio/dio.dart';
import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/features/favourites/provider/favourite_providers.dart';
import 'package:gomama/app/features/listing/model/all_listings_input.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:gomama/app/features/listing/model/update_favourite_listings_input.dart';
import 'package:gomama/app/features/listing/repository/listing_repository.dart';
import 'package:gomama/app/features/listing/widget/filter_sheet.dart';
import 'package:gomama/app/features/maps/provider/position_providers.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'user_favourite_providers.g.dart';

@Riverpod(keepAlive: true)
class UserFavouriteListings extends _$UserFavouriteListings {
  // TODO(kkcy): save a copy to local storage
  @override
  List<String> build() {
    return [];
  }

  void set(List<String> value) {
    state = value;
  }

  void add(String value) {
    state = [...state, value];
  }

  void remove(String value) {
    state = state.where((element) => element != value).toList();
  }
}

@riverpod
Future<bool> updateFavouriteListings(
  UpdateFavouriteListingsRef ref,
  UpdateFavouriteListingsInput input,
) async {
  final result =
      await ref.read(listingRepositoryProvider).updateFavouriteListings(input);

  if (result.success) {
    Groveman.debug(
      'Successfully Added/Removed Listing to/from favorite',
      error: input.listingId,
    );

    final position = ref.read(currentPositionProvider).valueOrNull;
    ref.invalidate(
      favouriteListingsPagesProvider(
        AllListingsPagination(
          page: 0,
          input: AllListingsInput(
            lon: position?.longitude ?? 103.8198, // Default to Singapore
            lat: position?.latitude ?? 1.3521,
          ),
        ),
      ),
    );
  }

  return result.success;
}
