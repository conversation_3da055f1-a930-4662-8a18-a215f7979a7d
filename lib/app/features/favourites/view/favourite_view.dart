import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/favourites/provider/favourite_providers.dart';
import 'package:gomama/app/features/listing/model/all_listings_input.dart';
import 'package:gomama/app/features/listing/model/listings.dart';
import 'package:gomama/app/features/listing/widget/filter_sheet.dart';
import 'package:gomama/app/features/listing/widget/listing_card.dart';
import 'package:gomama/app/features/maps/provider/position_providers.dart';
import 'package:gomama/app/widgets/brand_bottom_sheet.dart';
import 'package:gomama/app/widgets/brand_scaffold.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'favourite_view.g.dart';

@riverpod
int _favouriteListingIndex(_FavouriteListingIndexRef ref) {
  throw UnimplementedError();
}

class FavouritesView extends HookConsumerWidget {
  const FavouritesView({super.key});

  static const routeName = 'favourites';
  static const routePath = '/favourites';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return BrandScaffold(
      title: const Text('Favourites'),
      actions: [
        IconButton(
          icon: const Icon(CustomIcon.filter),
          style: FilledButton.styleFrom(
            backgroundColor: Colors.white,
            foregroundColor: CustomColors.primary,
          ),
          onPressed: () {
            showCupertinoModalPopup(
              context: context,
              builder: (context) {
                return const FilterSheet();
              },
            );
          },
        ),
        const SizedBox(width: 16),
      ],
      physics: const NeverScrollableScrollPhysics(),
      child: BrandBottomSheet(
        minHeight: mediaQuery(context).size.height / 2,
        maxHeight: max(
              mediaQuery(context).viewPadding.top,
              kToolbarHeight / 2,
            ) +
            100,
        slivers: const [
          _FavouriteListings(),
        ],
      ),
    );
  }
}

class _FavouriteListings extends ConsumerWidget {
  const _FavouriteListings();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref
      ..watch(amenityFiltersProvider)
      ..watch(listingSortsProvider);

    final position = ref.watch(currentPositionProvider).valueOrNull;
    final listingCount = ref.watch(
      favouriteListingsCountProvider(
        AllListingsInput(
          lon: position?.longitude ?? 103.8198, // Default to Singapore
          lat: position?.latitude ?? 1.3521,
        ),
      ),
    );

    return listingCount.when(
      loading: () => const SliverToBoxAdapter(
        child: Center(child: CircularProgressIndicator()),
      ),
      error: (err, stack) => SliverToBoxAdapter(child: Text('Error $err')),
      data: (listingCount) {
        if (listingCount == 0) {
          return SliverToBoxAdapter(
            child: SizedBox(
              height: 300,
              child: Center(
                child: Column(
                  children: [
                    const SizedBox(height: 64),
                    Image.asset(
                      'assets/images/goma_sad.png',
                      height: 160,
                    ),
                    const Text('There are no favourites.'),
                  ],
                ),
              ),
            ),
          );
        }

        return SliverPadding(
          padding: const EdgeInsets.symmetric(vertical: 32),
          sliver: SliverList.separated(
            itemBuilder: (context, index) {
              return ProviderScope(
                overrides: [
                  // ignore: scoped_providers_should_specify_dependencies
                  _favouriteListingIndexProvider.overrideWithValue(index),
                ],
                child: const _ListingCard(),
              );
            },
            separatorBuilder: (context, index) {
              return const SizedBox(height: 16);
            },
            itemCount: listingCount,
          ),
        );
      },
    );
  }
}

class _ListingCard extends ConsumerWidget {
  const _ListingCard();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final position = ref.watch(currentPositionProvider).valueOrNull;
    final index = ref.watch(_favouriteListingIndexProvider);
    final offset = AllListingsOffset(
      offset: index,
      input: AllListingsInput(
        lon: position?.longitude ?? 103.8198, // Default to Singapore
        lat: position?.latitude ?? 1.3521,
      ),
    );
    final listing = ref.watch(
      favouriteListingAtIndexProvider(offset),
    );

    return listing.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (err, stack) => Text('Error $err'),
      data: (listing) {
        return ListingCard(listing);
      },
    );
  }
}
