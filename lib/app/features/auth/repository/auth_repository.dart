import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/features/auth/model/auth.dart';
import 'package:gomama/app/features/auth/model/user.dart';
import 'package:gomama/app/features/favourites/provider/user_favourite_providers.dart';
import 'package:groveman/groveman.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'auth_repository.g.dart';

@Riverpod(keepAlive: true)
AuthRepository authRepository(AuthRepositoryRef ref) => AuthRepository(ref);

class AuthRepository {
  AuthRepository(this.ref);
  final AuthRepositoryRef ref;

  // Future<Json> checkUserAvailability(String email) async {
  //   try {
  //     final response = await ref.read(repositoryProvider).post(
  //           '/users/available',
  //           body: jsonEncode({
  //             'email': email,
  //           }),
  //         );

  //     return response;
  //   } catch (error) {
  //     Groveman.warning('checkUserAvailability', error: error);
  //     rethrow;
  //   }
  // }

  // Future<User> getMyself() async {
  //   try {
  //     final response = await ref.read(repositoryProvider).get<Json>('/me');

  //     return PartialUser.fromJson(response.data!);
  //   } catch (error) {
  //     Groveman.warning('getMyself', error: error);
  //     rethrow;
  //   }
  // }

  Future<User> signIn(AuthInput input) async {
    try {
      final response = await ref.read(repositoryProvider).post<Json>(
            '/login',
            data: input.toJson(),
          );

      // transform favorite listings to [string]
      if (response.data!['data']['favorite_listings'] != null) {
        final listings =
            (response.data!['data']['favorite_listings'] as List<dynamic>)
                .map((item) {
                  return (item as Json)['id'];
                })
                .toList()
                .cast<String>();

        ref.read(userFavouriteListingsProvider.notifier).set(listings);
      }

      final authResponse =
          AuthResponse.fromJson(response.data!['data'] as Json);

      return SignedIn.fromJson({
        ...authResponse.user.toJson(),
        'token': authResponse.token.token,
        'realtime_jwt': authResponse.token.realtimeJwt,
      });
    } catch (error) {
      Groveman.warning('signIn', error: error);
      rethrow;
    }
  }

  Future<User> signInWithOtp(AuthInput input) async {
    try {
      final response = await ref.read(repositoryProvider).post<Json>(
            '/login-otp',
            data: input.toJson(),
          );

      // transform favorite listings to [string]
      if (response.data!['data']['favorite_listings'] != null) {
        final listings =
            (response.data!['data']['favorite_listings'] as List<dynamic>)
                .map((item) {
                  return (item as Json)['id'];
                })
                .toList()
                .cast<String>();

        ref.read(userFavouriteListingsProvider.notifier).set(listings);
      }

      final authResponse =
          AuthResponse.fromJson(response.data!['data'] as Json);

      return SignedIn.fromJson({
        ...authResponse.user.toJson(),
        'token': authResponse.token.token,
        'realtime_jwt': authResponse.token.realtimeJwt,
      });
    } catch (error) {
      Groveman.warning('signInWithOtp', error: error);
      rethrow;
    }
  }

  Future<User> signInWithFacebook(AuthInput input) async {
    try {
      final response = await ref.read(repositoryProvider).post<Json>(
            '/oauth/facebook',
            data: input.toJson(),
          );

      // transform favorite listings to [string]
      if (response.data!['data']['favorite_listings'] != null) {
        final listings =
            (response.data!['data']['favorite_listings'] as List<dynamic>)
                .map((item) {
                  return (item as Json)['id'];
                })
                .toList()
                .cast<String>();

        ref.read(userFavouriteListingsProvider.notifier).set(listings);
      }

      final authResponse =
          AuthResponse.fromJson(response.data!['data'] as Json);

      return SignedIn.fromJson({
        ...authResponse.user.toJson(),
        'token': authResponse.token.token,
        'realtime_jwt': authResponse.token.realtimeJwt,
      });
    } catch (error) {
      Groveman.warning('signInWithFacebook', error: error);
      rethrow;
    }
  }

  Future<User> signInWithGoogle(AuthInput input) async {
    try {
      final response = await ref.read(repositoryProvider).post<Json>(
            '/oauth/google',
            data: input.toJson(),
          );

      // transform favorite listings to [string]
      if (response.data!['data']['favorite_listings'] != null) {
        final listings =
            (response.data!['data']['favorite_listings'] as List<dynamic>)
                .map((item) {
                  return (item as Json)['id'];
                })
                .toList()
                .cast<String>();

        ref.read(userFavouriteListingsProvider.notifier).set(listings);
      }

      final authResponse =
          AuthResponse.fromJson(response.data!['data'] as Json);

      return SignedIn.fromJson({
        ...authResponse.user.toJson(),
        'token': authResponse.token.token,
        'realtime_jwt': authResponse.token.realtimeJwt,
      });
    } catch (error) {
      Groveman.warning('signInWithGoogle', error: error);
      rethrow;
    }
  }

  Future<User> signInWithApple(AuthInput input) async {
    try {
      final response = await ref.read(repositoryProvider).post<Json>(
            '/oauth/apple',
            data: input.toJson(),
          );

      // transform favorite listings to [string]
      if (response.data!['data']['favorite_listings'] != null) {
        final listings =
            (response.data!['data']['favorite_listings'] as List<dynamic>)
                .map((item) {
                  return (item as Json)['id'];
                })
                .toList()
                .cast<String>();

        ref.read(userFavouriteListingsProvider.notifier).set(listings);
      }

      final authResponse =
          AuthResponse.fromJson(response.data!['data'] as Json);

      return SignedIn.fromJson({
        ...authResponse.user.toJson(),
        'token': authResponse.token.token,
        'realtime_jwt': authResponse.token.realtimeJwt,
      });
    } catch (error) {
      Groveman.warning('signInWithGoogle', error: error);
      rethrow;
    }
  }

  Future<bool> signOut() async {
    try {
      final response =
          await ref.read(repositoryProvider).post<Json>('/me/logout');

      return response.data!['success'] == true;
    } catch (error) {
      Groveman.warning('signOut', error: error);
      rethrow;
    }
  }

  // Future<Json> signUp(RegisterInput input) async {
  //   try {
  //     final response = await ref.read(repositoryProvider).post(
  //           '/auth/login',
  //           body: jsonEncode(input.toJson()),
  //         );

  //     return response;
  //   } catch (error) {
  //     Groveman.warning('signUp', error: error);
  //     rethrow;
  //   }
  // }

  // Future<Json> sendAuthCode(String email) async {
  //   try {
  //     final response = await ref.read(repositoryProvider).post(
  //           '/auth/code',
  //           body: jsonEncode({
  //             'email': email,
  //           }),
  //         );

  //     return response;
  //   } catch (error) {
  //     Groveman.warning('sendAuthCode', error: error);
  //     rethrow;
  //   }
  // }

  // Future<Json> verifyAuthCode(VerifyCodeInput input) async {
  //   try {
  //     final response = await ref.read(repositoryProvider).post(
  //           '/auth/code/verify',
  //           body: jsonEncode(input.toJson()),
  //         );

  //     return response;
  //   } catch (error) {
  //     Groveman.warning('verifyAuthCode', error: error);
  //     rethrow;
  //   }
  // }

  // Future<Json> signInWithGoogle(String accessToken) async {
  //   try {
  //     final response = await ref.read(repositoryProvider).post(
  //           '/auth/google',
  //           body: jsonEncode({
  //             'access_token': accessToken,
  //           }),
  //         );

  //     return response;
  //   } catch (error) {
  //     Groveman.warning('signInWithGoogle', error: error);
  //     rethrow;
  //   }
  // }

  // Future<Json> signInWithApple(
  //   AuthorizationCredentialAppleID credential,
  // ) async {
  //   try {
  //     final response = await ref.read(repositoryProvider).post(
  //           '/auth/apple',
  //           body: jsonEncode(
  //             {
  //               'access_token': credential.authorizationCode,
  //               'first_name': credential.givenName,
  //               'last_name': credential.familyName,
  //             },
  //           ),
  //         );

  //     return response;
  //   } catch (error) {
  //     Groveman.warning('signInWithApple', error: error);
  //     rethrow;
  //   }
  // }

  // Future<User> changePassword(ChangePasswordInput input) async {
  //   try {
  //     final response = await ref.read(repositoryProvider).post(
  //           '/auth/change_password',
  //           body: jsonEncode(input.toJson()),
  //         );

  //     return User.fromJson(response['user'] as Json);
  //   } catch (error) {
  //     Groveman.warning('changePassword', error: error);
  //     rethrow;
  //   }
  // }

  // Future<bool> resetPassword(ForgotPasswordInput input) async {
  //   try {
  //     final response = await ref.read(repositoryProvider).post(
  //           '/auth/forgot_password',
  //           body: jsonEncode(input.toJson()),
  //         );

  //     return response['user'] != null;
  //   } catch (error) {
  //     Groveman.warning('resetPassword', error: error);
  //     rethrow;
  //   }
  // }

  // Future<Json> refreshToken() async {
  //   try {
  //     final response = await ref.read(repositoryProvider).post(
  //           '/auth/refresh-token',
  //         );

  //     return response;
  //   } catch (error) {
  //     Groveman.warning('refreshToken', error: error);
  //     rethrow;
  //   }
  // }
}
