import 'dart:io';

import 'package:app_settings/app_settings.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:form_builder_phone_field/form_builder_phone_field.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gomama/app/core/constants/api_exception.dart';
import 'package:gomama/app/core/constants/custom_colors.dart';
import 'package:gomama/app/core/constants/custom_icon_icons.dart';
import 'package:gomama/app/core/constants/environment.dart';
import 'package:gomama/app/core/utils/custom_validators.dart';
import 'package:gomama/app/core/utils/theme_style.dart';
import 'package:gomama/app/features/auth/model/auth.dart';
import 'package:gomama/app/features/auth/provider/auth_providers.dart';
import 'package:gomama/app/features/auth/provider/otp_providers.dart';
import 'package:gomama/app/features/auth/view/profile_view.dart';
import 'package:gomama/app/features/main/provider/main_providers.dart';
import 'package:gomama/app/widgets/adaptive_text_button.dart';
import 'package:gomama/app/widgets/loading_view.dart';
import 'package:groveman/groveman.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

enum AuthFormState {
  initial,
  otpLoading,
  socialLoginLoading,
}

class AuthForm extends HookConsumerWidget {
  const AuthForm({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authFlowController = ref.watch(authFlowControllerProvider.notifier);
    final _formKey = useState(GlobalKey<FormBuilderState>());
    final authFormState = useState<AuthFormState>(AuthFormState.initial);
    final countryCodeState = useState<String>('');
    final _errorMsg = useState<String?>(null);
    final loginWith = useState<String>('email');

    return FormBuilder(
      key: _formKey.value,
      child: Stack(
        children: [
          SizedBox(
            width: mediaQuery(context).size.width,
            height: mediaQuery(context).size.height,
          ),
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Column(
                children: [
                  Image.asset(
                    'assets/images/gomama_logo.png',
                    width: 250,
                  ),
                  const SizedBox(height: 40),
                  Text(
                    'Welcome!',
                    style: textTheme(context).titleLarge!.copyWith(
                          color: CustomColors.primary,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 20),
                  if (loginWith.value == 'email')
                    SizedBox(
                      height: _errorMsg.value == null ? 70 : null,
                      child: FormBuilderTextField(
                        name: 'email',
                        decoration: InputDecoration(
                          labelText: 'Email Address',
                          floatingLabelBehavior: FloatingLabelBehavior.always,
                          focusedBorder: const UnderlineInputBorder(
                            borderSide: BorderSide(
                              color: CustomColors.placeholder,
                            ),
                          ),
                          enabledBorder: const UnderlineInputBorder(
                            borderSide: BorderSide(
                              color: CustomColors.placeholder,
                            ),
                          ),
                          errorText: _errorMsg.value,
                        ),
                        autocorrect: false,
                        keyboardType: TextInputType.emailAddress,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Missing email address';
                          }

                          if (isValidEmail(value)) {
                            return null; // Valid email format
                          }

                          return 'Invalid email address';
                        },
                      ),
                    )
                  else if (loginWith.value == 'phone')
                    SizedBox(
                      height: _errorMsg.value == null ? 70 : null,
                      child: FormBuilderPhoneField(
                        name: 'mobileNumber',
                        defaultSelectedCountryIsoCode: 'SG',
                        priorityListByIsoCode: const ['SG'],
                        decoration: InputDecoration(
                          labelText: 'Mobile Number',
                          floatingLabelBehavior: FloatingLabelBehavior.always,
                          errorText: _errorMsg.value,
                          enabledBorder: const UnderlineInputBorder(
                            borderSide: BorderSide(
                              color: CustomColors.placeholder,
                            ),
                          ),
                          errorBorder: const UnderlineInputBorder(
                            borderSide: BorderSide(
                              color: CustomColors.red,
                            ),
                          ),
                          contentPadding: EdgeInsets.zero,
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Missing phone number';
                          }

                          return null;

                          // if (isValidMobileNumber(value)) {
                          //   return null; // Valid email format
                          // }

                          // return 'Invalid phone number';
                        },
                        textInputAction: TextInputAction.next,
                        countryPicker: (flag, countryCode) {
                          return Builder(
                            builder: (context) {
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                countryCodeState.value = countryCode;
                              });

                              return Row(
                                children: [
                                  const Icon(
                                    CustomIcon.keyboardArrowDown,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(countryCode),
                                  const SizedBox(width: 4),
                                ],
                              );
                            },
                          );
                        },
                      ),
                    ),
                  const SizedBox(height: 20),
                  SizedBox(
                    width: double.infinity,
                    child: TextButton(
                      style: TextButton.styleFrom(
                        backgroundColor: CustomColors.primary,
                        foregroundColor: Colors.white,
                      ),
                      onPressed: () async {
                        _errorMsg.value =
                            null; // Reset custom error checking message to null to avoid form validate error.
                        // Validate and save the form values
                        final success =
                            _formKey.value.currentState?.saveAndValidate();

                        if (success != true) {
                          return;
                        }

                        authFormState.value = AuthFormState.otpLoading;

                        try {
                          await authFlowController.requestOtp(
                            _formKey.value.currentState?.value['email']
                                .toString(),
                            _formKey.value.currentState?.value['mobileNumber']
                                .toString(),
                            countryCodeState.value,
                            loginWith.value == 'email',
                          );
                        } catch (e) {
                          if (e is AppNetworkResponseException &&
                              e.statusCode == 422) {
                            _errorMsg.value =
                                'Invalid ${loginWith.value == 'phone' ? 'phone' : 'email'} number';
                          } else {
                            _errorMsg.value =
                                'Unknown error, please try again with proper phone number';
                          }

                          // TODO(kkcy): handle failed to send OTP
                          Groveman.warning('auth_form otp', error: e);
                        } finally {
                          authFormState.value = AuthFormState.initial;
                        }
                      },
                      child: Text(
                        authFormState.value == AuthFormState.otpLoading
                            ? 'Sending...'
                            : 'Send Verification Code',
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                  Row(
                    children: [
                      const Expanded(
                        child: Divider(),
                      ),
                      const SizedBox(width: 24),
                      Text(
                        'or',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                        ),
                      ),
                      const SizedBox(width: 24),
                      const Expanded(
                        child: Divider(),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (loginWith.value == 'email')
                        IconButton(
                          style: IconButton.styleFrom(
                            backgroundColor: Colors.transparent,
                            foregroundColor: CustomColors.primaries,
                          ),
                          onPressed: () async {
                            loginWith.value = 'phone';
                          },
                          icon: const Icon(Icons.sms_sharp, size: 36),
                        )
                      else
                        IconButton(
                          style: IconButton.styleFrom(
                            backgroundColor: Colors.transparent,
                            foregroundColor: CustomColors.primaries,
                          ),
                          onPressed: () async {
                            loginWith.value = 'email';
                          },
                          icon: const Icon(Icons.email_sharp, size: 36),
                        ),
                      const SizedBox(width: 16),
                      if (Platform.isIOS) ...[
                        _AppleLoginButton(
                          setAuthFormState: (state) {
                            authFormState.value = state;
                          },
                        ),
                        const SizedBox(width: 16),
                      ],
                      _FacebookLoginButton(
                        setAuthFormState: (state) {
                          authFormState.value = state;
                        },
                      ),
                      const SizedBox(width: 16),
                      _GoogleLoginButton(
                        setAuthFormState: (state) {
                          authFormState.value = state;
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  ref
                          .watch(clientAppVersionProvider)
                          .whenData(
                            (val) =>
                                Center(child: Text('v$val')),
                          )
                          .asData
                          ?.valueOrNull ??
                      const SizedBox.shrink(),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
          if (authFormState.value != AuthFormState.initial)
            Positioned.fill(
              child: DecoratedBox(
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.4),
                ),
                child: const Center(
                  child: SizedBox(
                    width: 160,
                    height: 160,
                    child: LoadingView(),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class _AppleLoginButton extends ConsumerWidget {
  const _AppleLoginButton({
    required this.setAuthFormState,
  });
  final Function(AuthFormState) setAuthFormState;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return IconButton(
      icon: Image.asset(
        'assets/images/apple_logo.png',
        width: 40,
        height: 40,
      ),
      onPressed: () async {
        try {
          await ref.read(authControllerProvider.notifier).loginWithApple(() {
            setAuthFormState(AuthFormState.socialLoginLoading);
          });
        } catch (error) {
          setAuthFormState(AuthFormState.initial);

          Groveman.error('Apple Login', error: error);

          if (context.mounted) {
            if (error is UnauthorizedException) {
              Groveman.error('Apple Login', error: error.message);

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(error.message),
                ),
              );
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Something went wrong'),
                ),
              );
            }
          }
        }
      },
    );
  }
}

class _FacebookLoginButton extends ConsumerWidget {
  const _FacebookLoginButton({
    required this.setAuthFormState,
  });
  final Function(AuthFormState) setAuthFormState;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return IconButton(
      icon: Image.asset(
        'assets/images/facebook_logo.png',
        width: 40,
        height: 40,
      ),
      onPressed: () async {
        try {
          await ref.read(authControllerProvider.notifier).loginWithFacebook(() {
            setAuthFormState(AuthFormState.socialLoginLoading);
          });
        } catch (error) {
          setAuthFormState(AuthFormState.initial);

          Groveman.error('Facebook Login', error: error);

          if (context.mounted) {
            if (error == LoginException.appTrackingTransparencyNotAuthorized) {
              // show dialog
              await showDialog(
                context: context,
                builder: (context) => AlertDialog.adaptive(
                  title: const Text('Login with Facebook'),
                  content: const Text(
                    'Please switch on "Allow Tracking" to continue login with Facebook',
                  ),
                  actions: [
                    AdaptiveTextButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      child: const Text('Cancel'),
                    ),
                    AdaptiveTextButton(
                      onPressed: () async {
                        await AppSettings.openAppSettings();
                      },
                      child: const Text('OK'),
                    ),
                  ],
                ),
              );
            } else if (error is UnauthorizedException) {
              Groveman.error('Facebook Login', error: error.message);

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(error.message),
                ),
              );
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Something went wrong'),
                ),
              );
            }
          }
        }
      },
    );
  }
}

class _GoogleLoginButton extends ConsumerWidget {
  const _GoogleLoginButton({
    required this.setAuthFormState,
  });
  final Function(AuthFormState) setAuthFormState;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return IconButton(
      icon: Image.asset(
        'assets/images/google_logo.png',
        width: 40,
        height: 40,
      ),
      onPressed: () async {
        try {
          await ref.read(authControllerProvider.notifier).loginWithGoogle(() {
            setAuthFormState(AuthFormState.socialLoginLoading);
          });
        } catch (error) {
          setAuthFormState(AuthFormState.initial);

          Groveman.error('Google Login', error: error);

          if (context.mounted) {
            if (error is UnauthorizedException) {
              Groveman.error('Google Login', error: error.message);

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(error.message),
                ),
              );
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Something went wrong'),
                ),
              );
            }
          }
        }
      },
    );
  }
}
