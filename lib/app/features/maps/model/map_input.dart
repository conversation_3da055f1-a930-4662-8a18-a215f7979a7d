import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gomama/app/core/constants/environment.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';

part 'map_input.freezed.dart';

@freezed
class MapInput with _$MapInput {
  factory MapInput({
    required double latitude,
    required double longitude,
    required double zoom,
  }) = _MapInput;
}

@freezed
class MapBoxMapState with _$MapBoxMapState {
  factory MapBoxMapState({
    MapboxMap? mapController,
    required double latitude,
    required double longitude,
    @Default(false) bool showMarker,
  }) = _MapBoxMapState;
}

// in km
double getSearchRadius(double zoomLevel) {
  if (Environment.flavor == 'development') {
    return 5000;
  }

  if (zoomLevel <= 3) return 5000; // 5000 km
  if (zoomLevel <= 6) return 1000; // 1000 km
  if (zoomLevel <= 9) return 200; // 200 km
  if (zoomLevel <= 11) return 50; // 50 km
  if (zoomLevel <= 13) return 10; // 10 km
  if (zoomLevel <= 15) return 2; // 2 km
  if (zoomLevel <= 17) return 1; // 500 m
  return 0.5; // 100 m
}
