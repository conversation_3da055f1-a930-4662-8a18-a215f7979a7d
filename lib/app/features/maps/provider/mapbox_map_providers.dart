import 'package:gomama/app/core/network/web_providers.dart';
import 'package:gomama/app/features/listing/repository/listing_repository.dart';
import 'package:gomama/app/features/maps/model/google_places.dart';
import 'package:gomama/app/features/maps/model/map_input.dart';
import 'package:gomama/app/features/maps/provider/google_places_providers.dart';
import 'package:gomama/app/features/maps/provider/position_providers.dart';
import 'package:groveman/groveman.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'mapbox_map_providers.g.dart';

@Riverpod(keepAlive: true)
Future<String> listingsGeojson(ListingsGeojsonRef ref) async {
  return ref.watch(listingRepositoryProvider).fetchListingsGeojson();
}

@riverpod
class MapboxMapStateController extends _$MapboxMapStateController {
  @override
  MapBoxMapState build() {
    // Use valueOrNull to gracefully handle permission denial
    final position = ref.read(currentPositionProvider).valueOrNull;

    return MapBoxMapState(
      latitude: position?.latitude ?? 1.3521,
      longitude: position?.longitude ?? 103.8198,
    );
  }

  void setController(MapboxMap? controller) {
    state = state.copyWith(mapController: controller);
  }

  Future<SanitisedGooglePlaceDetail?> onMoved(
    double latitude,
    double longitude,
  ) async {
    if (state.showMarker) {
      state = state.copyWith(latitude: latitude, longitude: longitude);

      // find the address for this location
      final place = await ref.read(
        getNearbyPlaceFromCoordinatesProvider(
          latitude,
          longitude,
        ).future,
      );

      Groveman.info('mapboxMapState.onMoved', error: place);

      return place;
    }

    return null;
  }

  Future<void> moveLocationAtMap(double latitude, double longitude) async {
    await state.mapController?.flyTo(
      CameraOptions(
        center: Point(
          coordinates: Position(
            longitude,
            latitude,
          ),
        ),
        zoom: 16,
      ),
      MapAnimationOptions(),
    );

    state = state.copyWith(
      latitude: latitude,
      longitude: longitude,
      showMarker: true,
    );
  }
}
