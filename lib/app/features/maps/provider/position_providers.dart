import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'position_providers.g.dart';

@riverpod
FutureOr<Position?> currentPosition(CurrentPositionRef ref) async {
  // Check permission status without requesting to avoid spam
  final permission = await Permission.locationWhenInUse.status;

  if (permission.isGranted) {
    try {
      return await Geolocator.getCurrentPosition();
    } catch (e) {
      // If location services are disabled or other error, return null
      return null;
    }
  }

  return null;
}
