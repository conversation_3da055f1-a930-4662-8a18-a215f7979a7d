# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**Gomama Flutter v2** is a customer-facing mobile application built with Flutter, designed for booking and managing services on the Gomama platform. The app supports real-time updates via WebSocket connections and integrates with Gomama's core APIs.

## Architecture & Structure

### Tech Stack
- **Framework**: Flutter 3.22.1 with Dart SDK >=3.0.1
- **State Management**: Riverpod + Hooks (hooks_riverpod)
- **Routing**: GoRouter with code generation
- **Networking**: Dio for HTTP, web_socket_channel for real-time
- **Local Storage**: Hive + Flutter Secure Storage
- **Firebase**: Core, Messaging, and Analytics
- **Maps**: Mapbox Maps Flutter
- **Commerce**: Shopify Flutter integration
- **Code Generation**: Freezed, JSON Serializable, Riverpod Generator

### Key Directory Structure
```
lib/
├── app/
│   ├── app.dart                 # Root MaterialApp.router
│   ├── core/                    # Core functionality
│   │   ├── constants/           # App constants, colors, extensions
│   │   ├── local_storage/       # Hive & secure storage setup
│   │   ├── network/             # MQTT, HTTP interceptors, API models
│   │   ├── router/              # GoRouter configuration
│   │   ├── theme/               # AppTheme provider
│   │   └── utils/               # Utilities, validators, providers
│   ├── features/                # Feature-based modules
│   │   ├── auth/                # Authentication flows
│   │   ├── commerce/            # E-commerce functionality
│   │   ├── listing/             # Service listings
│   │   ├── session/             # Booking sessions
│   │   └── [other features]/
│   └── widgets/                 # Reusable UI components
├── l10n/                        # Internationalization
└── main_*.dart                  # Environment-specific entry points
```

## Development Setup

### Prerequisites
- Flutter 3.22.1 (specified in pubspec.yaml)
- Dart SDK >=3.0.1
- Android Studio / Xcode for mobile builds
- Firebase configuration files (google-services.json, GoogleService-Info.plist)

### Environment Configuration
The app uses three environments with separate configurations:
- **Development**: `.env.development`
- **Staging**: `.env.staging` 
- **Production**: `.env.production`

### Commands

#### Setup & Dependencies
```bash
# Install dependencies
flutter pub get

# Generate code (models, providers, routes)
fvm flutter pub run build_runner build --delete-conflicting-outputs

# Generate localization
flutter gen-l10n

# Generate app icons
flutter pub run icons_launcher:create

# Generate splash screens
flutter pub run flutter_native_splash:create
```

#### Running the App
```bash
# Development
flutter run --flavor development --target lib/main_development.dart

# Staging  
flutter run --flavor staging --target lib/main_staging.dart

# Production
flutter run --flavor production --target lib/main_production.dart
```

#### Testing
```bash
# Run all tests
flutter test

# Run specific test file
flutter test test/app/view/app_test.dart

# Run tests with coverage
flutter test --coverage
```

#### Building
```bash
# Development APK
flutter build apk --flavor development --target lib/main_development.dart

# Production APK
flutter build apk --flavor production --target lib/main_production.dart

# iOS builds require Xcode
flutter build ios --flavor production --target lib/main_production.dart
```

## Key Patterns & Conventions

### State Management
- **Riverpod**: Primary state management solution with code generation
- **Hooks**: Used for local widget state and effects
- **Providers**: Auto-generated providers for repositories and services
- **Models**: Freezed classes with JSON serialization

### Architecture Patterns
- **Feature-based structure**: Each feature has its own directory with models, providers, views, and widgets
- **Repository pattern**: Data access layer with generated providers
- **Service layer**: MQTT, HTTP, and platform services
- **Router-based navigation**: GoRouter with typed routes

### Code Generation
The project heavily uses code generation:
- **Freezed**: For immutable data classes
- **Riverpod Generator**: For providers
- **GoRouter Builder**: For typed routing
- **JSON Serializable**: For JSON serialization

Always run `build_runner` after modifying annotated classes:
```bash
flutter pub run build_runner build --delete-conflicting-outputs
```

### Environment Variables
Key environment variables in `.env` files:
- `HTTP_URL`: Base API URL
- `SHOPIFY_ACCESS_TOKEN`: Shopify storefront token
- `SHOPIFY_STORE`: Shopify store name
- `MAPBOX_ACCESS_TOKEN`: Mapbox access token

### Custom Lint Rules
- Uses `very_good_analysis` as base
- Custom lint configuration in `analysis_options.yaml`
- Ignores: `public_member_api_docs`, `lines_longer_than_80_chars`

### Asset Structure
- **assets/**: General assets (images, backgrounds, etc.)
- **assets/maps/**: Map markers and related assets
- **assets/rives/**: Rive animations
- **assets/videos/**: Video assets
- **fonts/**: Custom fonts (AveriaSansLibre, Quicksand, SimplySerif)

## Build Configuration

### Flavors
The app is configured with three flavors:
- **development**: Debug builds with development backend
- **staging**: Staging builds with staging backend  
- **production**: Release builds with production backend

### Platform Configuration
- **Android**: Build flavors configured in `android/app/build.gradle`
- **iOS**: Build configurations in `ios/Runner.xcodeproj`
- **Web**: Basic PWA configuration in `web/manifest.json`

## Common Development Tasks

### Adding a New Feature
1. Create feature directory under `lib/app/features/`
2. Add models with Freezed annotations
3. Add providers with Riverpod annotations
4. Add views and widgets
5. Update router configuration if needed
6. Run code generation

### Adding Environment Variables
1. Add to all `.env` files (development, staging, production)
2. Update `Environment` class in constants
3. Update build configurations if needed

### Localization
- Strings are managed in `lib/l10n/arb/` files
- Use `AppLocalizations.of(context)` for localized strings
- Run `flutter gen-l10n` after modifications

## Testing
- **Unit tests**: Standard Flutter test framework
- **Widget tests**: Use `pumpApp` helper from `test/helpers/pump_app.dart`
- **Integration tests**: Not currently configured
- **Coverage**: Generate with `flutter test --coverage`

## Troubleshooting

### Common Issues
1. **Build failures**: Ensure environment files exist and Flutter version matches
2. **Code generation**: Always run build_runner after model changes
3. **iOS builds**: Check Firebase configuration files are present
4. **Dependencies**: Use `flutter clean` and `flutter pub get` for dependency issues

### Environment-specific Issues
- **Development**: Check `.env.development` file exists
- **Firebase**: Ensure Firebase configuration matches environment
- **API endpoints**: Verify correct URLs in environment files